import { paperClasses } from '@mui/material/Paper';
import type { Components } from '@mui/material/styles';

import type { Theme } from '../types';

export const MuiCard = {
  styleOverrides: {
    root: ({ theme }) => {
      return {
        borderRadius: '12px', // Updated to match design system
        [`&.${paperClasses.elevation1}`]: {
          boxShadow:
            theme.palette.mode === 'dark'
              ? '0 2px 6px rgba(0, 0, 0, 0.4)' // Updated shadow for dark theme
              : '0 5px 22px 0 rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(0, 0, 0, 0.06)',
        },
        // Add hover state for better interactivity
        '&:hover': {
          [`&.${paperClasses.elevation1}`]: {
            boxShadow:
              theme.palette.mode === 'dark'
                ? '0 4px 12px rgba(0, 0, 0, 0.5)' // Enhanced shadow on hover
                : '0 8px 32px 0 rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(0, 0, 0, 0.08)',
          },
        },
      };
    },
  },
} satisfies Components<Theme>['MuiCard'];
