model Role {
    id        String   @id @default(uuid())
    name      String?
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    workspaceId String?
    workspace   Workspace? @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

    memberships WorkspaceMembership[]
    permissions Permissions[]         @relation("RolePermissions")

    workspaceInvi WorkspaceInvite[] @ignore
}

model Permissions {
    id    String @id
    roles Role[] @relation("RolePermissions")
}

enum Permission {
    DELETE_WORKSPACE
    INVITE_MEMBER
    UPDATE_WORKSPACE
    MANAGE_INTEGRATIONS
}
