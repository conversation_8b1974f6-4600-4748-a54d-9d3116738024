{"name": "bms-tech-pulse-frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "typecheck": "tsc --noEmit", "format": "prettier --check \"**/*.{js,jsx,mjs,ts,tsx,mdx,json,css}\" --cache", "format:fix": "prettier --write \"**/*.{js,jsx,mjs,ts,tsx,mdx,json,css}\" --cache", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "npm run lint -- --fix", "style": "npm run format && npm run lint && npm run typecheck", "style:fix": "npm run format:fix && npm run lint:fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage --coverageDirectory=TestResults/coverage", "db:init": "prisma migrate dev --name init", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:reset": "prisma migrate reset", "db:push": "prisma db push", "db:pull": "prisma db pull", "db:seed": "prisma db seed", "db:studio": "prisma studio", "local:setup": "zx local.mjs", "postinstall": "npm run db:generate"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "11.14.0", "@emotion/server": "11.11.0", "@emotion/styled": "11.14.1", "@fontsource/inter": "5.2.6", "@fontsource/plus-jakarta-sans": "5.2.6", "@fontsource/roboto-mono": "5.2.6", "@google-cloud/pubsub": "^5.1.0", "@hookform/resolvers": "5.1.1", "@mui/lab": "7.0.0-beta.14", "@mui/material": "7.1.2", "@mui/material-nextjs": "^7.1.1", "@mui/system": "7.1.1", "@mui/utils": "7.1.1", "@mui/x-date-pickers": "8.6.0", "@octokit/app": "^16.0.1", "@octokit/webhooks-types": "^7.6.1", "@phosphor-icons/react": "2.1.10", "@prisma/client": "^6.10.1", "apexcharts": "4.7.0", "axios": "1.10.0", "cookies-next": "^6.0.0", "dayjs": "1.11.13", "firebase": "^11.9.1", "firebase-admin": "^13.4.0", "i18n-iso-countries": "^7.14.0", "libphonenumber-js": "^1.12.9", "mui-tel-input": "^9.0.1", "next": "^15.3.4", "next-intl": "^4.3.1", "next-swagger-doc": "^0.4.1", "octokit": "^5.0.3", "prisma": "^6.10.1", "prisma-openapi": "^1.3.3", "react": "19.1.0", "react-apexcharts": "1.7.0", "react-dom": "19.1.0", "react-hook-form": "7.59.0", "swagger-ui-dist": "^5.25.3", "zod": "3.25.67"}, "devDependencies": {"@eslint/js": "^9.30.0", "@next/eslint-plugin-next": "^15.3.4", "@octokit/types": "^14.1.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "30.0.0", "@types/mapbox-gl": "3.4.1", "@types/node": "24.0.7", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/react-syntax-highlighter": "15.5.13", "@types/swagger-ui-dist": "^3.30.6", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "eslint": "9.30.0", "eslint-config-next": "15.3.4", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsdoc": "^51.2.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-tailwindcss": "^3.18.0", "globals": "^16.2.0", "jest": "30.0.3", "jest-environment-jsdom": "30.0.2", "jest-html-reporter": "^4.3.0", "jest-junit": "^16.0.0", "prettier": "3.6.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.13", "tsx": "^4.20.3", "typescript": "5.8.3", "zx": "^8.6.0"}, "scarfSettings": {"enabled": false}, "engines": {"node": ">=22.0.0"}, "prisma": {"seed": "tsx prisma/seed.ts", "schema": "prisma"}}