import React from 'react';
import { ThemeProvider } from '@mui/material/styles';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';

import { useApiServices } from '@/hooks/use-api-services';
import { createTheme } from '@/styles/theme/create-theme';
import { PendingInvitations } from '../pending-invitations';

// Mock next-intl with proper translations
jest.mock('next-intl', () => ({
  useTranslations: (namespace: string) => (key: string) => {
    const translations: Record<string, Record<string, string>> = {
      'workspace.invitations': {
        title: 'Invitations',
        description: 'You have been invited to join these workspaces',
        loading: 'Loading invitations...',
        noInvitations: 'No pending invitations',
        noInvitationsDescription: 'You don\'t have any pending workspace invitations at the moment.',
        invitedBy: 'Invited by {name}',
        accept: 'Accept',
        decline: 'Decline',
        accepting: 'Accepting...',
        declining: 'Declining...',
        pending: 'Pending',
      },
    };
    return translations[namespace]?.[key] || key;
  },
}));

// Mock the useApiServices hook
jest.mock('@/hooks/use-api-services', () => ({
  useApiServices: jest.fn(),
}));

const mockUseApiServices = useApiServices as jest.MockedFunction<typeof useApiServices>;

// Mock data
const mockInvitations = [
  {
    id: 'invite-1',
    email: '<EMAIL>',
    workspaceId: 'workspace-1',
    invitedBy: 'inviter-1',
    userId: 'user-1',
    roleId: 'member',
    status: 'PENDING' as const,
    createdAt: new Date(),
    updatedAt: new Date(),
    token: null,
    workspace: {
      id: 'workspace-1',
      name: 'Test Workspace',
      avatar: null,
    },
    invitedByUser: {
      id: 'inviter-1',
      email: '<EMAIL>',
      displayName: 'Inviter User',
    },
    role: {
      id: 'member',
      name: 'Member',
    },
  },
  {
    id: 'invite-2',
    email: '<EMAIL>',
    workspaceId: 'workspace-2',
    invitedBy: 'inviter-2',
    userId: 'user-1',
    roleId: 'admin',
    status: 'PENDING' as const,
    createdAt: new Date(),
    updatedAt: new Date(),
    token: null,
    workspace: {
      id: 'workspace-2',
      name: 'Another Workspace',
      avatar: 'https://example.com/avatar.jpg',
    },
    invitedByUser: {
      id: 'inviter-2',
      email: '<EMAIL>',
      displayName: 'Another Inviter',
    },
    role: {
      id: 'admin',
      name: 'Admin',
    },
  },
];

const mockWorkspaceApiService = {
  getUserInvitations: jest.fn(),
  acceptInvitation: jest.fn(),
  declineInvitation: jest.fn(),
};

// Helper function to render component with theme
function renderWithTheme(component: React.ReactElement) {
  const theme = createTheme();
  return render(<ThemeProvider theme={theme}>{component}</ThemeProvider>);
}

describe('PendingInvitations', () => {
  const mockOnInvitationAccepted = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseApiServices.mockReturnValue({
      workspaceApiService: mockWorkspaceApiService,
    } as any);
  });

  it('should render pending invitations', async () => {
    mockWorkspaceApiService.getUserInvitations.mockResolvedValue(mockInvitations);

    renderWithTheme(<PendingInvitations onInvitationAccepted={mockOnInvitationAccepted} />);

    await waitFor(() => {
      expect(screen.getByText('Invitations (2)')).toBeInTheDocument();
      expect(screen.getByText('Test Workspace')).toBeInTheDocument();
      expect(screen.getByText('Another Workspace')).toBeInTheDocument();
    });

    // Check role chips
    expect(screen.getByText('Member')).toBeInTheDocument();
    expect(screen.getByText('Admin')).toBeInTheDocument();

    // Check invited by text
    expect(screen.getByText('Invited by Inviter User')).toBeInTheDocument();
    expect(screen.getByText('Invited by Another Inviter')).toBeInTheDocument();
  });

  it('should not render when no invitations exist', async () => {
    mockWorkspaceApiService.getUserInvitations.mockResolvedValue([]);

    renderWithTheme(<PendingInvitations onInvitationAccepted={mockOnInvitationAccepted} />);

    await waitFor(() => {
      expect(screen.queryByText('workspace.invitations.title')).not.toBeInTheDocument();
    });
  });

  it('should handle accept invitation', async () => {
    mockWorkspaceApiService.getUserInvitations.mockResolvedValue(mockInvitations);
    mockWorkspaceApiService.acceptInvitation.mockResolvedValue({
      message: 'Invitation accepted',
      membership: {},
    });

    renderWithTheme(<PendingInvitations onInvitationAccepted={mockOnInvitationAccepted} />);

    await waitFor(() => {
      expect(screen.getByText('Test Workspace')).toBeInTheDocument();
    });

    // Click accept button for first invitation
    const acceptButtons = screen.getAllByText('Accept');
    fireEvent.click(acceptButtons[0]);

    await waitFor(() => {
      expect(mockWorkspaceApiService.acceptInvitation).toHaveBeenCalledWith('invite-1');
      expect(mockOnInvitationAccepted).toHaveBeenCalled();
    });
  });

  it('should handle decline invitation', async () => {
    mockWorkspaceApiService.getUserInvitations.mockResolvedValue(mockInvitations);
    mockWorkspaceApiService.declineInvitation.mockResolvedValue({
      message: 'Invitation declined',
      invitation: mockInvitations[0],
    });

    renderWithTheme(<PendingInvitations onInvitationAccepted={mockOnInvitationAccepted} />);

    await waitFor(() => {
      expect(screen.getByText('Test Workspace')).toBeInTheDocument();
    });

    // Click decline button for first invitation
    const declineButtons = screen.getAllByText('Decline');
    fireEvent.click(declineButtons[0]);

    await waitFor(() => {
      expect(mockWorkspaceApiService.declineInvitation).toHaveBeenCalledWith('invite-1');
    });

    // Should remove the invitation from the list
    await waitFor(() => {
      expect(screen.queryByText('Test Workspace')).not.toBeInTheDocument();
      expect(screen.getByText('Another Workspace')).toBeInTheDocument();
    });
  });

  it('should show loading state during actions', async () => {
    mockWorkspaceApiService.getUserInvitations.mockResolvedValue(mockInvitations);
    mockWorkspaceApiService.acceptInvitation.mockImplementation(
      () => new Promise((resolve) => setTimeout(resolve, 100))
    );

    renderWithTheme(<PendingInvitations onInvitationAccepted={mockOnInvitationAccepted} />);

    await waitFor(() => {
      expect(screen.getByText('Test Workspace')).toBeInTheDocument();
    });

    // Click accept button
    const acceptButtons = screen.getAllByText('Accept');
    fireEvent.click(acceptButtons[0]);

    // Should show loading state
    expect(acceptButtons[0]).toBeDisabled();
  });

  it('should handle API errors gracefully', async () => {
    mockWorkspaceApiService.getUserInvitations.mockResolvedValue(mockInvitations);
    mockWorkspaceApiService.acceptInvitation.mockRejectedValue(new Error('API Error'));

    renderWithTheme(<PendingInvitations onInvitationAccepted={mockOnInvitationAccepted} />);

    await waitFor(() => {
      expect(screen.getByText('Test Workspace')).toBeInTheDocument();
    });

    // Click accept button
    const acceptButtons = screen.getAllByText('Accept');
    fireEvent.click(acceptButtons[0]);

    await waitFor(() => {
      expect(mockWorkspaceApiService.acceptInvitation).toHaveBeenCalledWith('invite-1');
      // Should not call onInvitationAccepted on error
      expect(mockOnInvitationAccepted).not.toHaveBeenCalled();
    });
  });

  it('should display workspace avatars correctly', async () => {
    mockWorkspaceApiService.getUserInvitations.mockResolvedValue(mockInvitations);

    renderWithTheme(<PendingInvitations onInvitationAccepted={mockOnInvitationAccepted} />);

    await waitFor(() => {
      // Check that we have the correct workspace names displayed
      expect(screen.getByText('Test Workspace')).toBeInTheDocument();
      expect(screen.getByText('Another Workspace')).toBeInTheDocument();

      // Check that we have the specific avatar for the second workspace
      const avatarWithSrc = screen.getByRole('img', { name: '' });
      expect(avatarWithSrc).toHaveAttribute('src', 'https://example.com/avatar.jpg');
    });
  });

  it('should handle invitations without role', async () => {
    const invitationWithoutRole = {
      ...mockInvitations[0],
      role: null,
    };

    mockWorkspaceApiService.getUserInvitations.mockResolvedValue([invitationWithoutRole]);

    renderWithTheme(<PendingInvitations onInvitationAccepted={mockOnInvitationAccepted} />);

    await waitFor(() => {
      expect(screen.getByText('Test Workspace')).toBeInTheDocument();
      // Should not show role chip when role is null
      expect(screen.queryByText('Member')).not.toBeInTheDocument();
    });
  });

  it('should handle loading state', async () => {
    mockWorkspaceApiService.getUserInvitations.mockImplementation(
      () => new Promise((resolve) => setTimeout(resolve, 100))
    );

    renderWithTheme(<PendingInvitations onInvitationAccepted={mockOnInvitationAccepted} />);

    // Should not render anything while loading
    expect(screen.queryByText('workspace.invitations.title')).not.toBeInTheDocument();
  });

  it('should handle fetch invitations error', async () => {
    mockWorkspaceApiService.getUserInvitations.mockRejectedValue(new Error('Fetch error'));

    renderWithTheme(<PendingInvitations onInvitationAccepted={mockOnInvitationAccepted} />);

    await waitFor(() => {
      // Should not render anything on fetch error
      expect(screen.queryByText('workspace.invitations.title')).not.toBeInTheDocument();
    });
  });
});
