/* Remove if fonts are not used */
@import '@fontsource/inter/100.css';
@import '@fontsource/inter/200.css';
@import '@fontsource/inter/300.css';
@import '@fontsource/inter/400.css';
@import '@fontsource/inter/500.css';
@import '@fontsource/inter/600.css';
@import '@fontsource/inter/700.css';
@import '@fontsource/inter/800.css';
@import '@fontsource/inter/900.css';
@import '@fontsource/roboto-mono/300.css';
@import '@fontsource/roboto-mono/400.css';
@import '@fontsource/plus-jakarta-sans/600.css';
@import '@fontsource/plus-jakarta-sans/700.css';

/* Variables */
:root {
  --icon-fontSize-sm: 1rem;
  --icon-fontSize-md: 1.25rem;
  --icon-fontSize-lg: 1.5rem;

  /* Midnight + Electric-Blue Design System Colors */
  --bg-primary: #0d1117; /* nearly-black midnight */
  --bg-surface: #161b22; /* elevated cards & sidebars */
  --border-subtle: #30363d;
  --text-primary: #f8fafc; /* 98% white */
  --text-muted: #94a3b8;

  /* Accent Colors */
  --accent-primary: #3b82f6; /* electric blue */
  --accent-hover: #2563eb;
  --accent-subtle: #1e40af;

  /* Semantic Colors */
  --success: #22c55e;
  --warning: #facc15;
  --danger: #ef4444;
  --info: #0ea5e9;

  /* Spacing Grid (8pt system) */
  --spacing-xs: 0.25rem; /* 4px */
  --spacing-sm: 0.5rem; /* 8px */
  --spacing-md: 1rem; /* 16px */
  --spacing-lg: 1.5rem; /* 24px */
  --spacing-xl: 2rem; /* 32px */
  --spacing-2xl: 3rem; /* 48px */

  /* Border Radius */
  --radius-card: 12px;
  --radius-button: 12px;

  /* Shadows */
  --shadow-card: 0 2px 6px rgba(0, 0, 0, 0.4);
  --shadow-card-hover: 0 4px 12px rgba(0, 0, 0, 0.5);
  --shadow-modal: 0 8px 32px rgba(0, 0, 0, 0.6);
}

*:focus-visible {
  outline: 2px solid var(--mui-palette-primary-main);
}

html {
  height: 100%;
}

body {
  height: 100%;
}
