'use client';

import { useRouter } from '@/i18n/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import Alert from '@mui/material/Alert';
import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Chip from '@mui/material/Chip';
import CircularProgress from '@mui/material/CircularProgress';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import InputLabel from '@mui/material/InputLabel';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import OutlinedInput from '@mui/material/OutlinedInput';
import Pagination from '@mui/material/Pagination';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Badge from '@mui/material/Badge';
import { BuildingsIcon } from '@phosphor-icons/react/dist/ssr/Buildings';
import { DotsThreeVerticalIcon } from '@phosphor-icons/react/dist/ssr/DotsThreeVertical';
import { MagnifyingGlassIcon } from '@phosphor-icons/react/dist/ssr/MagnifyingGlass';
import { EnvelopeIcon } from '@phosphor-icons/react/dist/ssr/Envelope';
import { XIcon } from '@phosphor-icons/react/dist/ssr/X';
import { PencilSimpleIcon } from '@phosphor-icons/react/dist/ssr/PencilSimple';
import { PlusIcon } from '@phosphor-icons/react/dist/ssr/Plus';
import { TrashIcon } from '@phosphor-icons/react/dist/ssr/Trash';
import { useTranslations } from 'next-intl';
import * as React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z as zod } from 'zod';

import { useWorkspace } from '@/contexts/workspace-context';
import { logger } from '@/lib/logger/default-logger';
import { paths } from '@/paths';
import { useSearchParams } from 'next/navigation';
import { useApiServices } from '@/hooks/use-api-services';
import { WorkspaceInvite, Workspace, User, Role } from '@prisma/client';

type PendingInviteWithDetails = WorkspaceInvite & {
  workspace: Workspace;
  invitedByUser: User;
  role: Role | null;
};

interface CreateWorkspaceFormData {
  name: string;
}

// Constants for pagination and search
const WORKSPACES_PER_PAGE = 12;
const SEARCH_THRESHOLD = 11;

// TODO: when workspace guard redirects to the workspace selection, save the url to returnTo.
// TODO: after selecting the workspace, redirect to the returnTo.
export function WorkspaceSelection(): React.JSX.Element {
  const t = useTranslations('workspace.selection');
  const tInvitations = useTranslations('workspace.invitations');
  const router = useRouter();
  const {
    workspaces,
    loading,
    error,
    currentWorkspace,
    selectWorkspace,
    createWorkspace,
    updateWorkspace,
    deleteWorkspace,
    clearError,
  } = useWorkspace();

  const { workspaceApiService } = useApiServices();
  const params = useSearchParams();

  // Tab management
  const [currentTab, setCurrentTab] = React.useState<'workspaces' | 'invitations'>('workspaces');

  // Invitations state
  const [invitations, setInvitations] = React.useState<PendingInviteWithDetails[]>([]);
  const [invitationsLoading, setInvitationsLoading] = React.useState(true);
  const [invitationsError, setInvitationsError] = React.useState<string | null>(null);
  const [processingInvites, setProcessingInvites] = React.useState<Set<string>>(new Set());

  // Workspace management state
  const [isCreating, setIsCreating] = React.useState(false);
  const [isEditing, setIsEditing] = React.useState(false);
  const [isDeleting, setIsDeleting] = React.useState(false);
  const [isSelecting, setIsSelecting] = React.useState<string | null>(null);
  const [pendingNavigation, setPendingNavigation] = React.useState(false);
  const [createDialogOpen, setCreateDialogOpen] = React.useState(false);
  const [editDialogOpen, setEditDialogOpen] = React.useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [selectedWorkspace, setSelectedWorkspace] = React.useState<any>(null);
  const [editWorkspaceName, setEditWorkspaceName] = React.useState('');
  const [editError, setEditError] = React.useState<string | null>(null);
  const [deleteError, setDeleteError] = React.useState<string | null>(null);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [currentPage, setCurrentPage] = React.useState(1);
  const [menuAnchor, setMenuAnchor] = React.useState<null | HTMLElement>(null);
  const [menuWorkspaceId, setMenuWorkspaceId] = React.useState<string | null>(null);
  const [highlightedWorkspaceId, setHighlightedWorkspaceId] = React.useState<string | null>(null);

  // Load pending invitations
  const loadInvitations = React.useCallback(async () => {
    try {
      setInvitationsLoading(true);
      setInvitationsError(null);
      const data = await workspaceApiService.getUserInvitations();
      setInvitations(data);
    } catch (err) {
      console.error('Error loading invitations:', err);
      setInvitationsError('Failed to load invitations');
    } finally {
      setInvitationsLoading(false);
    }
  }, [workspaceApiService]);

  // Load invitations on mount
  React.useEffect(() => {
    loadInvitations();
  }, [loadInvitations]);

  // Handle accepting invitation
  const handleAcceptInvitation = async (inviteId: string) => {
    try {
      setProcessingInvites(prev => new Set(prev).add(inviteId));
      await workspaceApiService.acceptInvitation(inviteId);

      // Remove the invitation from the list
      setInvitations(prev => prev.filter(inv => inv.id !== inviteId));

      // Refresh workspaces when an invitation is accepted
      window.location.reload();
    } catch (err) {
      console.error('Error accepting invitation:', err);
      setInvitationsError('Failed to accept invitation');
    } finally {
      setProcessingInvites(prev => {
        const newSet = new Set(prev);
        newSet.delete(inviteId);
        return newSet;
      });
    }
  };

  // Handle declining invitation
  const handleDeclineInvitation = async (inviteId: string) => {
    try {
      setProcessingInvites(prev => new Set(prev).add(inviteId));
      await workspaceApiService.declineInvitation(inviteId);

      // Remove the invitation from the list
      setInvitations(prev => prev.filter(inv => inv.id !== inviteId));
    } catch (err) {
      console.error('Error declining invitation:', err);
      setInvitationsError('Failed to decline invitation');
    } finally {
      setProcessingInvites(prev => {
        const newSet = new Set(prev);
        newSet.delete(inviteId);
        return newSet;
      });
    }
  };

  // Create schema with translations
  const schema = React.useMemo(() => {
    return zod.object({
      name: zod.string().min(1, { message: t('workspaceNameRequired') }),
    });
  }, [t]);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CreateWorkspaceFormData>({
    defaultValues: { name: '' },
    resolver: zodResolver(schema),
  });

  // Filter workspaces based on search query
  const filteredWorkspaces = React.useMemo(() => {
    if (!searchQuery.trim()) {
      return workspaces;
    }
    return workspaces.filter((workspace) => workspace.name?.toLowerCase().includes(searchQuery.toLowerCase()));
  }, [workspaces, searchQuery]);

  // Calculate pagination
  const totalPages = Math.ceil(filteredWorkspaces.length / WORKSPACES_PER_PAGE);
  const startIndex = (currentPage - 1) * WORKSPACES_PER_PAGE;
  const endIndex = startIndex + WORKSPACES_PER_PAGE;
  const paginatedWorkspaces = filteredWorkspaces.slice(startIndex, endIndex);

  // Show search and pagination if there are enough workspaces or if there's a workspaceName param
  const showSearchAndPagination = workspaces.length > SEARCH_THRESHOLD || params.has('workspaceName');

  // Reset page when search changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  // Handle query parameters
  React.useEffect(() => {
    // Handle workspaceName parameter - set search query
    const nameParam = params.get('workspaceName');
    if (nameParam) {
      setSearchQuery(nameParam);
    }

    // Handle workspaceId parameter - highlight card
    const idParam = params.get('workspaceId');
    if (idParam) {
      setHighlightedWorkspaceId(idParam);
    }
  }, [params]); // Only run when params change

  // Handle navigation after workspace selection
  React.useEffect(() => {
    logger.debug('[WorkspaceSelection]: Navigation effect triggered', {
      pendingNavigation,
      currentWorkspace: currentWorkspace?.id,
      loading,
      shouldNavigate: pendingNavigation && currentWorkspace && !loading,
    });

    if (pendingNavigation && currentWorkspace && !loading) {
      logger.debug('[WorkspaceSelection]: Navigating to home page');
      setPendingNavigation(false);

      // Add a small delay to ensure all state updates are complete
      setTimeout(() => {
        logger.debug('[WorkspaceSelection]: Actually navigating now');

        const returnTo = params.get('returnTo') ?? '';

        logger.debug(`[WorkspaceSelection]: Redirecting to ${returnTo ? 'returnTo' : paths.root}`, {
          returnTo,
        });

        if (returnTo) {
          router.push(returnTo as any);
          return;
        }

        router.push(paths.root);
      }, 100);
    }
  }, [pendingNavigation, currentWorkspace, loading, router, params]);

  // Menu handlers
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, workspaceId: string) => {
    event.stopPropagation();
    setMenuAnchor(event.currentTarget);
    setMenuWorkspaceId(workspaceId);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setMenuWorkspaceId(null);
  };

  const handleEditWorkspace = () => {
    const workspace = workspaces.find((w) => w.id === menuWorkspaceId);
    if (workspace) {
      setSelectedWorkspace(workspace);
      setEditWorkspaceName(workspace.name || '');
      setEditError(null); // Clear any previous errors
      setEditDialogOpen(true);
    }
    handleMenuClose();
  };

  const handleDeleteWorkspace = () => {
    const workspace = workspaces.find((w) => w.id === menuWorkspaceId);
    if (workspace) {
      setSelectedWorkspace(workspace);
      setDeleteError(null); // Clear any previous errors
      setDeleteDialogOpen(true);
    }
    handleMenuClose();
  };

  // Save workspace changes
  const handleSaveWorkspace = async () => {
    if (!selectedWorkspace || !editWorkspaceName.trim()) return;

    try {
      setIsEditing(true);
      setEditError(null);
      clearError(); // Clear any context errors
      await updateWorkspace(selectedWorkspace.id, { name: editWorkspaceName.trim() });
      setEditDialogOpen(false);
      setSelectedWorkspace(null);
      setEditWorkspaceName('');
    } catch (updateError) {
      console.error('Error updating workspace:', updateError);
      setEditError(t('errors.editFailed'));
    } finally {
      setIsEditing(false);
    }
  };

  // Confirm delete workspace
  const handleConfirmDelete = async () => {
    if (!selectedWorkspace) return;

    try {
      setIsDeleting(true);
      setDeleteError(null);
      clearError(); // Clear any context errors
      await deleteWorkspace(selectedWorkspace.id);
      setDeleteDialogOpen(false);
      setSelectedWorkspace(null);
    } catch (deleteErr) {
      console.error('Error deleting workspace:', deleteErr);
      setDeleteError(t('errors.deleteFailed'));
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle workspace selection
  const handleSelectWorkspace = React.useCallback(
    async (workspaceId: string) => {
      logger.debug('[WorkspaceSelection]: Starting workspace selection', { workspaceId });

      try {
        setIsSelecting(workspaceId);
        setPendingNavigation(true);
        logger.debug('[WorkspaceSelection]: Calling selectWorkspace API');
        await selectWorkspace(workspaceId);
        logger.debug('[WorkspaceSelection]: selectWorkspace API completed successfully');
      } catch (selectError) {
        console.error('[WorkspaceSelection]: Error selecting workspace:', selectError);
        setPendingNavigation(false);
        setIsSelecting(null);
      }
    },
    [selectWorkspace]
  );

  // Handle workspace creation
  const handleCreateWorkspace = React.useCallback(
    async (data: CreateWorkspaceFormData) => {
      try {
        setIsCreating(true);
        await createWorkspace({ name: data.name });
        setCreateDialogOpen(false);
        reset();
      } catch (createError) {
        console.error('Error creating workspace:', createError);
        // Error is handled by the context
      } finally {
        setIsCreating(false);
      }
    },
    [createWorkspace, reset]
  );

  // Handle dialog close
  const handleCloseDialog = React.useCallback(() => {
    setCreateDialogOpen(false);
    reset();
    clearError();
  }, [reset, clearError]);

  // Render loading state
  if (loading && workspaces.length === 0) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '60vh',
          gap: 2,
        }}
      >
        <CircularProgress />
        <Typography variant='body2' color='text.secondary'>
          {t('loadingWorkspaces')}
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      <Stack spacing={4}>
        {/* Header */}
        <Stack spacing={2} alignItems='center' textAlign='center'>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: (theme) => `${theme.palette.primary.main}15`,
              color: 'primary.main',
              borderRadius: '50%',
              p: 2,
              width: 80,
              height: 80,
            }}
          >
            <BuildingsIcon fontSize='var(--icon-fontSize-xl)' />
          </Box>
          <Typography variant='h4' component='h1'>
            {t('title')}
          </Typography>
          <Typography variant='body1' color='text.secondary' sx={{ maxWidth: 600 }}>
            {t('description')}
          </Typography>
        </Stack>

        {/* Error Alert - Only show if no modals are open */}
        {error && !createDialogOpen && !editDialogOpen && !deleteDialogOpen && (
          <Alert severity='error' onClose={clearError}>
            {error.message}
          </Alert>
        )}

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={currentTab}
            onChange={(_, newValue) => setCurrentTab(newValue)}
            aria-label="workspace selection tabs"
            sx={{
              '& .MuiTabs-indicator': {
                backgroundColor: 'primary.main',
              },
            }}
          >
            <Tab
              label={t('title')}
              value="workspaces"
              icon={<BuildingsIcon size={20} />}
              iconPosition="start"
              sx={{
                textTransform: 'none',
                fontWeight: 600,
                minHeight: 48,
              }}
            />
            <Tab
              label={
                <Badge
                  badgeContent={invitations.length}
                  color="primary"
                  sx={{
                    '& .MuiBadge-badge': {
                      right: -8,
                      top: -2,
                    },
                  }}
                >
                  {tInvitations('title')}
                </Badge>
              }
              value="invitations"
              icon={<EnvelopeIcon size={20} />}
              iconPosition="start"
              sx={{
                textTransform: 'none',
                fontWeight: 600,
                minHeight: 48,
              }}
            />
          </Tabs>
        </Box>

        {/* Tab Content */}
        {currentTab === 'workspaces' && (
          <>
            {/* Action Bar with Search and Create Button - Only show when pagination is needed */}
        {showSearchAndPagination && (
          <Box
            sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}
          >
            {/* Search Bar */}
            <TextField
              placeholder={t('searchWorkspaces')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position='start'>
                      <MagnifyingGlassIcon />
                    </InputAdornment>
                  ),
                },
              }}
              sx={{ minWidth: 300, maxWidth: 400 }}
            />

            {/* Create New Workspace Button */}
            <Button
              variant='contained'
              size='large'
              startIcon={<PlusIcon />}
              onClick={() => setCreateDialogOpen(true)}
              sx={{
                px: 3,
                py: 1.5,
                fontSize: '1rem',
                fontWeight: 600,
              }}
            >
              {t('createWorkspace')}
            </Button>
          </Box>
        )}

        {/* Workspaces Grid */}
        {filteredWorkspaces.length > 0 ? (
          <>
            <Grid container spacing={4} justifyContent='center'>
              {paginatedWorkspaces.map((workspace) => (
                <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={workspace.id}>
                  <Card
                    id={`workspace-${workspace.id}`}
                    sx={{
                      transition: 'all 0.2s',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      position: 'relative',
                      ...(highlightedWorkspaceId === workspace.id && {
                        border: '2px solid',
                        borderColor: 'primary.main',
                        boxShadow: (theme) => theme.shadows[4],
                      }),
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: (theme) => theme.shadows[4],
                        '& [data-workspace-actions]': {
                          opacity: 1,
                        },
                      },
                    }}
                  >
                    {/* Action Menu Button */}
                    <IconButton
                      data-workspace-actions
                      sx={{
                        position: 'absolute',
                        top: 12,
                        right: 12,
                        opacity: menuWorkspaceId === workspace.id ? 1 : 0,
                        transition: 'all 0.2s',
                        bgcolor: 'background.paper',
                        backdropFilter: 'blur(8px)',
                        border: '1px solid',
                        borderColor: 'divider',
                        width: 32,
                        height: 32,
                        color: 'text.primary',
                        '&:hover': {
                          bgcolor: 'action.hover',
                          borderColor: 'primary.main',
                          transform: 'scale(1.05)',
                          boxShadow: (theme) => theme.shadows[4],
                        },
                      }}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleMenuOpen(e, workspace.id);
                      }}
                      size='small'
                    >
                      <DotsThreeVerticalIcon size={16} />
                    </IconButton>

                    <CardContent
                      sx={{
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column',
                        p: 3,
                        '&:last-child': { pb: 3 },
                      }}
                    >
                      <Stack spacing={3} alignItems='center' textAlign='center' sx={{ flex: 1 }}>
                        <Avatar
                          sx={{
                            width: 72,
                            height: 72,
                            bgcolor: 'primary.main',
                            fontSize: '1.75rem',
                            fontWeight: 600,
                          }}
                          src={workspace.avatar || undefined}
                        >
                          {workspace.name?.charAt(0).toUpperCase() || 'W'}
                        </Avatar>
                        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                          <Typography
                            variant='h6'
                            component='h3'
                            sx={{
                              fontWeight: 600,
                              mb: 1,
                              lineHeight: 1.3,
                              wordBreak: 'break-word',
                            }}
                          >
                            {workspace.name || 'Unnamed Workspace'}
                          </Typography>
                        </Box>
                        <Button
                          variant='outlined'
                          size='medium'
                          fullWidth
                          disabled={isSelecting === workspace.id}
                          startIcon={isSelecting === workspace.id ? <CircularProgress size={16} /> : null}
                          sx={{ mt: 'auto' }}
                          onClick={() => handleSelectWorkspace(workspace.id)}
                        >
                          {isSelecting === workspace.id ? t('selecting') : t('selectWorkspace')}
                        </Button>
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              ))}

              {/* Create Workspace Card - Only show when no pagination */}
              {!showSearchAndPagination && (
                <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
                  <Card
                    sx={{
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      border: '2px dashed',
                      borderColor: 'divider',
                      bgcolor: 'transparent',
                      '&:hover': {
                        borderColor: 'primary.main',
                        bgcolor: (theme) => `${theme.palette.primary.main}08`,
                        transform: 'translateY(-2px)',
                        boxShadow: (theme) => theme.shadows[4],
                      },
                    }}
                    onClick={() => setCreateDialogOpen(true)}
                  >
                    <CardContent
                      sx={{
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column',
                        p: 3,
                        '&:last-child': { pb: 3 },
                      }}
                    >
                      <Stack
                        spacing={3}
                        alignItems='center'
                        textAlign='center'
                        sx={{ flex: 1, justifyContent: 'center' }}
                      >
                        <Avatar
                          sx={{
                            width: 72,
                            height: 72,
                            bgcolor: 'primary.main',
                            fontSize: '1.75rem',
                            fontWeight: 600,
                          }}
                        >
                          <PlusIcon size={32} />
                        </Avatar>
                        <Box>
                          <Typography
                            variant='h6'
                            component='h3'
                            sx={{
                              fontWeight: 600,
                              mb: 1,
                              lineHeight: 1.3,
                            }}
                          >
                            {t('createWorkspace')}
                          </Typography>
                          <Typography variant='body2' color='text.secondary'>
                            {t('createWorkspaceCardDescription')}
                          </Typography>
                        </Box>
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              )}
            </Grid>

            {/* Pagination */}
            {showSearchAndPagination && totalPages > 1 && (
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                <Pagination
                  count={totalPages}
                  page={currentPage}
                  onChange={(_, page) => setCurrentPage(page)}
                  color='primary'
                />
              </Box>
            )}
          </>
        ) : searchQuery ? (
          /* No Search Results */
          <Stack spacing={3} alignItems='center' textAlign='center'>
            <Typography variant='h6'>{t('noSearchResults')}</Typography>
            <Typography variant='body2' color='text.secondary' sx={{ maxWidth: 500 }}>
              {t('noSearchResultsDescription')}
            </Typography>
          </Stack>
        ) : (
          /* No Workspaces State - Show create workspace card */
          <Grid container spacing={4} justifyContent='center'>
            <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
              <Card
                sx={{
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  border: '2px dashed',
                  borderColor: 'divider',
                  bgcolor: 'transparent',
                  '&:hover': {
                    borderColor: 'primary.main',
                    bgcolor: (theme) => `${theme.palette.primary.main}08`,
                    transform: 'translateY(-2px)',
                    boxShadow: (theme) => theme.shadows[4],
                  },
                }}
                onClick={() => setCreateDialogOpen(true)}
              >
                <CardContent
                  sx={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    p: 3,
                    '&:last-child': { pb: 3 },
                  }}
                >
                  <Stack spacing={3} alignItems='center' textAlign='center' sx={{ flex: 1, justifyContent: 'center' }}>
                    <Avatar
                      sx={{
                        width: 72,
                        height: 72,
                        bgcolor: 'primary.main',
                        fontSize: '1.75rem',
                        fontWeight: 600,
                      }}
                    >
                      <PlusIcon size={32} />
                    </Avatar>
                    <Box>
                      <Typography
                        variant='h6'
                        component='h3'
                        sx={{
                          fontWeight: 600,
                          mb: 1,
                          lineHeight: 1.3,
                        }}
                      >
                        {t('createWorkspace')}
                      </Typography>
                      <Typography variant='body2' color='text.secondary'>
                        {t('noWorkspacesDescription')}
                      </Typography>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}
          </>
        )}

        {/* Invitations Tab Content */}
        {currentTab === 'invitations' && (
          <Box sx={{ py: 2 }}>
            {invitationsLoading ? (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  py: 8,
                  gap: 2,
                }}
              >
                <CircularProgress />
                <Typography variant='body2' color='text.secondary'>
                  {tInvitations('loading')}
                </Typography>
              </Box>
            ) : invitationsError ? (
              <Alert severity='error' sx={{ mb: 2 }}>
                {invitationsError}
              </Alert>
            ) : invitations.length === 0 ? (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  py: 8,
                  gap: 2,
                }}
              >
                <EnvelopeIcon size={48} style={{ opacity: 0.5 }} />
                <Typography variant='h6' color='text.secondary'>
                  {tInvitations('noInvitations')}
                </Typography>
                <Typography variant='body2' color='text.secondary' textAlign='center'>
                  {tInvitations('noInvitationsDescription')}
                </Typography>
              </Box>
            ) : (
              <Grid container spacing={3}>
                {invitations.map((invitation) => (
                  <Grid key={invitation.id} size={{ xs: 12, sm: 6, md: 4 }}>
                    <Card
                      sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        transition: 'all 0.2s ease-in-out',
                        border: '2px solid',
                        borderColor: 'divider',
                        '&:hover': {
                          borderColor: 'primary.main',
                          transform: 'translateY(-2px)',
                          boxShadow: (theme) => theme.shadows[4],
                        },
                      }}
                    >
                      <CardContent sx={{ flex: 1, p: 3 }}>
                        <Stack spacing={2}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar
                              sx={{
                                width: 48,
                                height: 48,
                                bgcolor: 'primary.main',
                                fontSize: '1.25rem',
                                fontWeight: 600,
                              }}
                            >
                              {(invitation.workspace.name || 'W').charAt(0).toUpperCase()}
                            </Avatar>
                            <Box sx={{ flex: 1, minWidth: 0 }}>
                              <Typography
                                variant='h6'
                                sx={{
                                  fontWeight: 600,
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  whiteSpace: 'nowrap',
                                }}
                              >
                                {invitation.workspace.name}
                              </Typography>
                              <Typography variant='body2' color='text.secondary'>
                                {tInvitations('invitedBy', { name: invitation.invitedByUser.name || invitation.invitedByUser.email })}
                              </Typography>
                            </Box>
                          </Box>

                          {invitation.role && (
                            <Chip
                              label={invitation.role.name}
                              size='small'
                              variant='outlined'
                              sx={{ alignSelf: 'flex-start' }}
                            />
                          )}

                          <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                            <Button
                              variant='contained'
                              size='small'
                              startIcon={<CheckIcon size={16} />}
                              onClick={() => handleAcceptInvitation(invitation.id)}
                              disabled={processingInvites.has(invitation.id)}
                              sx={{ flex: 1 }}
                            >
                              {processingInvites.has(invitation.id) ? tInvitations('accepting') : tInvitations('accept')}
                            </Button>
                            <Button
                              variant='outlined'
                              size='small'
                              startIcon={<XIcon size={16} />}
                              onClick={() => handleDeclineInvitation(invitation.id)}
                              disabled={processingInvites.has(invitation.id)}
                              sx={{ flex: 1 }}
                            >
                              {processingInvites.has(invitation.id) ? tInvitations('declining') : tInvitations('decline')}
                            </Button>
                          </Box>
                        </Stack>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        )}
      </Stack>
      {/* Action Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleEditWorkspace}>
          <PencilSimpleIcon style={{ marginRight: 8 }} />
          {t('editWorkspace')}
        </MenuItem>
        <MenuItem onClick={handleDeleteWorkspace} sx={{ color: 'error.main' }}>
          <TrashIcon style={{ marginRight: 8 }} />
          {t('deleteWorkspace')}
        </MenuItem>
      </Menu>
      {/* Create Workspace Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={handleCloseDialog}
        maxWidth='sm'
        fullWidth
        slotProps={{
          paper: {
            sx: {
              borderRadius: 2,
              boxShadow: (theme) => theme.shadows[10],
            },
          },
        }}
      >
        <DialogTitle sx={{ pb: 2 }}>
          <Typography variant='h5' component='div' sx={{ fontWeight: 600 }}>
            {t('createWorkspaceTitle')}
          </Typography>
          <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
            {t('createWorkspaceDescription')}
          </Typography>
        </DialogTitle>

        <form onSubmit={handleSubmit(handleCreateWorkspace)}>
          <DialogContent sx={{ pt: 3, pb: 2 }}>
            <Stack spacing={4}>
              {/* Avatar Selection Placeholder */}
              <Box>
                <Typography variant='subtitle2' sx={{ mb: 1 }}>
                  {t('workspaceAvatar')}
                </Typography>
                <Stack direction='row' alignItems='center' spacing={2}>
                  <Avatar
                    sx={{
                      width: 64,
                      height: 64,
                      bgcolor: 'primary.main',
                      cursor: 'pointer',
                      border: '2px dashed',
                      borderColor: 'divider',
                      '&:hover': {
                        borderColor: 'primary.main',
                        bgcolor: 'primary.light',
                      },
                    }}
                  >
                    <PlusIcon size={24} />
                  </Avatar>
                  <Box>
                    <Typography variant='body2' color='text.secondary'>
                      {t('uploadAvatarDescription')}
                    </Typography>
                    <Typography variant='caption' color='text.secondary'>
                      {t('avatarRequirements')}
                    </Typography>
                  </Box>
                </Stack>
              </Box>

              {/* Workspace Name */}
              <Controller
                control={control}
                name='name'
                render={({ field }) => (
                  <FormControl fullWidth error={Boolean(errors.name)}>
                    <InputLabel>{t('workspaceName')}</InputLabel>
                    <OutlinedInput {...field} label={t('workspaceName')} placeholder={t('workspaceNamePlaceholder')} />
                    {errors.name && <FormHelperText>{errors.name.message}</FormHelperText>}
                  </FormControl>
                )}
              />
            </Stack>
          </DialogContent>

          <DialogActions sx={{ px: 3, pb: 3 }}>
            <Button onClick={handleCloseDialog} disabled={isCreating} size='large'>
              {t('cancelButton')}
            </Button>
            <Button
              type='submit'
              variant='contained'
              disabled={isCreating}
              size='large'
              sx={{ minWidth: 120 }}
              startIcon={isCreating ? <CircularProgress size={16} /> : null}
            >
              {isCreating ? t('creating') : t('createButton')}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
      {/* Edit Workspace Dialog */}
      <Dialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        maxWidth='sm'
        fullWidth
        slotProps={{
          paper: {
            sx: {
              borderRadius: 2,
              boxShadow: (theme) => theme.shadows[10],
            },
          },
        }}
      >
        <DialogTitle sx={{ pb: 2 }}>
          <Typography variant='h5' component='div' sx={{ fontWeight: 600 }}>
            {t('editWorkspaceTitle')}
          </Typography>
          <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
            {t('editWorkspaceDescription')}
          </Typography>
        </DialogTitle>

        <DialogContent sx={{ pt: 3, pb: 2 }}>
          <Stack spacing={4}>
            {/* Error Alert */}
            {editError && (
              <Alert severity='error' onClose={() => setEditError(null)}>
                {editError}
              </Alert>
            )}
            {/* Avatar Selection */}
            <Box>
              <Typography variant='subtitle2' sx={{ mb: 1 }}>
                {t('workspaceAvatar')}
              </Typography>
              <Stack direction='row' alignItems='center' spacing={2}>
                <Avatar
                  sx={{
                    width: 64,
                    height: 64,
                    cursor: 'pointer',
                    border: '2px solid',
                    borderColor: 'divider',
                    '&:hover': {
                      borderColor: 'primary.main',
                    },
                  }}
                  src={selectedWorkspace?.avatar}
                >
                  {selectedWorkspace?.name?.charAt(0).toUpperCase() || 'W'}
                </Avatar>
                <Box>
                  <Button variant='outlined' size='small'>
                    {t('changeAvatar')}
                  </Button>
                  <Typography variant='caption' display='block' color='text.secondary' sx={{ mt: 0.5 }}>
                    {t('avatarRequirements')}
                  </Typography>
                </Box>
              </Stack>
            </Box>

            {/* Workspace Name */}
            <TextField
              fullWidth
              label={t('workspaceName')}
              value={editWorkspaceName}
              onChange={(e) => setEditWorkspaceName(e.target.value)}
              placeholder={t('workspaceNamePlaceholder')}
            />
          </Stack>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button onClick={() => setEditDialogOpen(false)} disabled={isEditing} size='large'>
            {t('cancelButton')}
          </Button>
          <Button
            variant='contained'
            size='large'
            sx={{ minWidth: 120 }}
            onClick={handleSaveWorkspace}
            disabled={isEditing || !editWorkspaceName.trim()}
            startIcon={isEditing ? <CircularProgress size={16} /> : null}
          >
            {isEditing ? t('saving') : t('saveChanges')}
          </Button>
        </DialogActions>
      </Dialog>
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth='xs'
        fullWidth
        slotProps={{
          paper: {
            sx: {
              borderRadius: 2,
              boxShadow: (theme) => theme.shadows[10],
            },
          },
        }}
      >
        <DialogTitle sx={{ pb: 2 }}>
          <Typography variant='h5' component='div' sx={{ fontWeight: 600, color: 'error.main' }}>
            {t('deleteWorkspaceTitle')}
          </Typography>
          <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
            {t('deleteWorkspaceDescription')}
          </Typography>
        </DialogTitle>

        <DialogContent sx={{ pt: 2 }}>
          <Stack spacing={2}>
            {/* Error Alert */}
            {deleteError && (
              <Alert severity='error' onClose={() => setDeleteError(null)}>
                {deleteError}
              </Alert>
            )}

            <Typography variant='body1'>
              {t('deleteConfirmation')} <strong>"{selectedWorkspace?.name}"</strong>?
            </Typography>
            <Typography variant='body2' color='text.secondary'>
              {t('deleteWarning')}
            </Typography>
          </Stack>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button onClick={() => setDeleteDialogOpen(false)} disabled={isDeleting} size='large'>
            {t('cancelButton')}
          </Button>
          <Button
            variant='contained'
            color='error'
            size='large'
            sx={{ minWidth: 120 }}
            onClick={handleConfirmDelete}
            disabled={isDeleting}
            startIcon={isDeleting ? <CircularProgress size={16} /> : null}
          >
            {isDeleting ? t('deleting') : t('deleteButton')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
