'use client';

import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Chip from '@mui/material/Chip';
import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { CheckIcon } from '@phosphor-icons/react/dist/ssr/Check';
import { XIcon } from '@phosphor-icons/react/dist/ssr/X';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { useApiServices } from '@/hooks/use-api-services';
import { WorkspaceInvite, Workspace, User } from '@prisma/client';

type PendingInviteWithDetails = WorkspaceInvite & {
  workspace: Workspace;
  invitedByUser: User;
};

interface PendingInvitationsProps {
  onInvitationAccepted?: () => void;
}

export function PendingInvitations({ onInvitationAccepted }: PendingInvitationsProps): React.JSX.Element {
  const t = useTranslations('workspace.invitations');
  const { workspaceApiService } = useApiServices();

  const [invitations, setInvitations] = React.useState<PendingInviteWithDetails[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [processingInvites, setProcessingInvites] = React.useState<Set<string>>(new Set());
  const [error, setError] = React.useState<string | null>(null);

  // Load pending invitations
  const loadInvitations = React.useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await workspaceApiService.getUserInvitations();
      setInvitations(data);
    } catch (err) {
      console.error('Error loading invitations:', err);
      setError('Failed to load invitations');
    } finally {
      setLoading(false);
    }
  }, [workspaceApiService]);

  // Load invitations on mount
  React.useEffect(() => {
    loadInvitations();
  }, [loadInvitations]);

  // Handle accepting invitation
  const handleAcceptInvitation = async (inviteId: string) => {
    try {
      setProcessingInvites(prev => new Set(prev).add(inviteId));
      await workspaceApiService.acceptInvitation(inviteId);
      
      // Remove the invitation from the list
      setInvitations(prev => prev.filter(inv => inv.id !== inviteId));
      
      // Notify parent component
      onInvitationAccepted?.();
    } catch (err) {
      console.error('Error accepting invitation:', err);
      setError('Failed to accept invitation');
    } finally {
      setProcessingInvites(prev => {
        const newSet = new Set(prev);
        newSet.delete(inviteId);
        return newSet;
      });
    }
  };

  // Handle declining invitation
  const handleDeclineInvitation = async (inviteId: string) => {
    try {
      setProcessingInvites(prev => new Set(prev).add(inviteId));
      await workspaceApiService.declineInvitation(inviteId);
      
      // Remove the invitation from the list
      setInvitations(prev => prev.filter(inv => inv.id !== inviteId));
    } catch (err) {
      console.error('Error declining invitation:', err);
      setError('Failed to decline invitation');
    } finally {
      setProcessingInvites(prev => {
        const newSet = new Set(prev);
        newSet.delete(inviteId);
        return newSet;
      });
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography color='error' gutterBottom>
          {error}
        </Typography>
        <Button onClick={loadInvitations} variant='outlined' size='small'>
          Retry
        </Button>
      </Box>
    );
  }

  if (invitations.length === 0) {
    return null; // Don't show anything if no invitations
  }

  return (
    <Box sx={{ mb: 4 }}>
      <Typography variant='h6' gutterBottom>
        {t('title')} ({invitations.length})
      </Typography>
      <Typography variant='body2' color='text.secondary' sx={{ mb: 3 }}>
        {t('description')}
      </Typography>

      <Grid container spacing={3}>
        {invitations.map((invitation) => {
          const isProcessing = processingInvites.has(invitation.id);
          
          return (
            <Grid size={{ xs: 12, sm: 6, md: 4 }} key={invitation.id}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  border: '2px solid',
                  borderColor: 'primary.main',
                  backgroundColor: 'primary.50',
                }}
              >
                <CardContent sx={{ flexGrow: 1 }}>
                  <Stack spacing={2}>
                    {/* Workspace Info */}
                    <Stack direction='row' spacing={2} alignItems='center'>
                      <Avatar
                        src={invitation.workspace.avatar || undefined}
                        sx={{ width: 48, height: 48 }}
                      >
                        {invitation.workspace.name.charAt(0).toUpperCase()}
                      </Avatar>
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant='h6' noWrap>
                          {invitation.workspace.name}
                        </Typography>
                        <Typography variant='body2' color='text.secondary'>
                          Invited by {invitation.invitedByUser.displayName || invitation.invitedByUser.email}
                        </Typography>
                      </Box>
                    </Stack>

                    {/* Invitation Status */}
                    <Chip
                      label={t('pending')}
                      color='warning'
                      size='small'
                      sx={{ alignSelf: 'flex-start' }}
                    />

                    {/* Date */}
                    <Typography variant='caption' color='text.secondary'>
                      Invited {new Date(invitation.createdAt).toLocaleDateString()}
                    </Typography>

                    {/* Action Buttons */}
                    <Stack direction='row' spacing={1} sx={{ mt: 'auto' }}>
                      <Button
                        variant='contained'
                        size='small'
                        startIcon={isProcessing ? <CircularProgress size={16} /> : <CheckIcon />}
                        onClick={() => handleAcceptInvitation(invitation.id)}
                        disabled={isProcessing}
                        sx={{ flex: 1 }}
                      >
                        {t('accept')}
                      </Button>
                      <Button
                        variant='outlined'
                        size='small'
                        startIcon={<XIcon />}
                        onClick={() => handleDeclineInvitation(invitation.id)}
                        disabled={isProcessing}
                        sx={{ flex: 1 }}
                      >
                        {t('decline')}
                      </Button>
                    </Stack>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>
    </Box>
  );
}
