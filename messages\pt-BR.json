{"settings": {"title": "Configurações", "categories": {"personal": "Pessoal", "workspace": "Espaço de Trabalho"}, "tabs": {"account": "Conta", "security": "Segurança", "notifications": "Notificações", "preferences": "Preferências", "billing": "Faturamento & planos", "team": "Equipe", "integrations": "Integrações"}, "preferences": {"title": "Preferências", "subheader": "Personalize sua experiência", "language": "Idioma", "languageLabel": "Selecione o idioma", "languageSwitch": "{locale, select, en_US {English} pt_BR {Português} es {Español} other {Unknown}}", "theme": "<PERSON><PERSON>", "themeOptions": {"light": "<PERSON><PERSON><PERSON>", "dark": "Escuro", "system": "Sistema"}, "preferences": {"title": "Defina suas preferências", "description": "Personalize sua experiência com o BMS Pulse."}}, "notifications": {"title": "Notificações", "subheader": "Gerenciar as notificaçõ<PERSON>", "email": "Email", "phone": "Telefone", "productUpdates": "Atualizações de produtos", "securityUpdates": "Atualizações de segurança", "saveChanges": "<PERSON><PERSON>"}, "password": {"title": "<PERSON><PERSON>", "subheader": "<PERSON><PERSON><PERSON><PERSON>", "currentPassword": "<PERSON><PERSON> atual", "newPassword": "Nova senha", "confirmPassword": "Confirmar nova senha", "update": "<PERSON><PERSON><PERSON><PERSON>", "currentPasswordRequired": "Senha atual é obrigatória", "newPasswordRequired": "Nova senha é obrigatória", "confirmPasswordRequired": "Por favor, confirme sua nova senha", "passwordMismatch": "As senhas não coincidem", "minPasswordLength": "A senha deve ter pelo menos 6 caracteres", "successMessage": "Senha atualizada com sucesso", "errors": {"updateFailed": "<PERSON><PERSON><PERSON> ao at<PERSON><PERSON><PERSON> senha", "wrongPassword": "Senha atual está incorreta", "weakPassword": "Nova senha é muito fraca", "requiresRecentLogin": "Por favor, saia e entre novamente antes de alterar sua senha"}}, "billing": {"title": "Faturamento & Planos", "subheader": "Gerenciar sua assinatura e métodos de pagamento", "currentPlan": "Plano Atual", "availablePlans": "Planos Disponíveis"}, "account": {"title": "Conta", "basicDetails": {"title": "Detalhes básicos", "subheader": "Gerencie suas informações pessoais", "displayName": "Nome", "email": "Endereço de email", "phone": "Número de telefone", "countryLabel": "<PERSON><PERSON>", "timezone": "<PERSON><PERSON>", "language": "Idioma", "saveChanges": "<PERSON><PERSON>", "countries": {"us": "Estados Unidos", "br": "Brasil", "gb": "Reino Unido", "ca": "Canadá", "jp": "Japão"}, "displayNameRequired": "O nome é obrigatório", "invalidEmail": "<PERSON><PERSON>", "invalidPhone": "Número de telefone inválido", "countryRequired": "<PERSON><PERSON>", "timezoneRequired": "O fuso horário <PERSON> inválido", "maxNameSize": "O nome deve ser menor que {size} caracteres", "avatarUploadError": "Erro no upload do avatar", "noChanges": "Nenhuma alteração realizada", "profileUpdatedSuccess": "Perfil atualizado com sucesso", "profileUpdateError": "Erro ao atualizar o perfil de usuário. Tente novamente mais tarde"}, "profilePicture": {"remove": "Remover", "upload": "<PERSON><PERSON><PERSON>", "uploadSuccess": "Foto de perfil atualizada com sucesso", "uploadError": "Falha ao carregar foto de perfil", "removeSuccess": "Foto de perfil removida com sucesso", "removeError": "Falha ao remover foto de perfil", "fileTooLarge": "O tamanho do arquivo deve ser menor que {size}MB", "invalidFileType": "Apenas arquivos PNG, JPEG e JPG são permitidos", "confirmRemove": "Tem certeza de que deseja remover sua foto de perfil?", "cancel": "<PERSON><PERSON><PERSON>", "fileReadError": "Erro de leitura do arquivo"}, "deleteAccount": {"title": "Excluir conta", "subheader": "Excluir permanentemente sua conta e todo o seu conteúdo", "description": "Esta ação não pode ser desfeita. Depois de excluir sua conta, todo o seu conteúdo será removido permanentemente.", "button": "Excluir conta"}}, "integrations": {"title": "Integrações", "loader": {"loadingText": "Carregando integrações..."}, "tabs": {"all": "<PERSON><PERSON>", "installed": "Instaladas", "available": "Não Instaladas"}, "updated": "Atualizado", "installs": "instalações", "search": "Buscar integração", "backToIntegrations": "Voltar para Integrações", "capabilitiesTitle": "Capacidades", "comingSoonMessage": "Esta integração estará disponível em breve!", "notifyMessage": "Estamos trabalhando duro para trazer esta integração para você. Gostaria de ser notificado quando estiver disponível?", "notifyButton": "Notificar-me", "noConfigAvailable": "Nenhuma configuração disponível para esta integração", "viewToggle": {"ariaLabel": "Modo de visualização", "card": "Visualização em cartões", "table": "Visualização em tabela"}, "status": {"available": "Disponível", "comingSoon": "Em breve", "installed": "Instalado"}, "table": {"name": "Nome", "description": "Descrição", "status": "Status", "actions": "Ações"}, "actions": {"add": "<PERSON><PERSON><PERSON><PERSON>", "import": "Importar", "export": "Exportar", "details": "<PERSON><PERSON><PERSON>", "install": "Instalar", "uninstall": "<PERSON><PERSON><PERSON><PERSON>", "uninstalling": "Desinstalando...", "save": "<PERSON><PERSON>", "installing": "Instalando..."}, "github": {"settings": "Configurações do GitHub", "syncFrequency": "Frequência de Sincronização", "syncFrequencyHelp": "Com que frequência devemos sincronizar dados dos seus repositórios do GitHub", "syncOptions": {"hourly": "A cada hora", "daily": "Diariamente", "weekly": "<PERSON><PERSON><PERSON><PERSON>"}, "webhookUrl": "URL do Webhook", "webhookUrlHelp": "Use esta URL nas configurações do seu GitHub App para receber eventos de webhook", "dangerZone": "Zona de Perigo", "disconnectWarning": "Desconectar removerá todos os dados do GitHub da sua conta. Esta ação não pode ser desfeita.", "disconnect": "Desconectar GitHub", "installations": "Instalações do GitHub", "refreshIntegrations": "Atualizar integrações", "noIntegrations": "Nenhuma organização ou usuário do GitHub encontrado. Conecte sua conta do GitHub para começar.", "installationsInfo": "As instalações do GitHub permitem que você conecte suas organizações ou contas pessoais do GitHub ao BMS Pulse. Isso nos dá acesso às métricas dos seus repositórios de acordo com as permissões necessárias para fornecer os melhores insights!\n\nTodos os dados são gerenciados com cuidado e segurança, e só temos acesso ao que realmente precisamos.", "installationDocs": "Saiba mais sobre instalações de Apps do GitHub", "account": "Conta", "type": "Tipo", "status": "Status", "connectedAt": "Conectado Em", "actions": "Ações", "organization": "Organização", "user": "<PERSON><PERSON><PERSON><PERSON>", "statusActive": "Ativo", "statusSuspended": "Suspenso", "statusUninstalled": "Desinstalado", "statusSynchronizing": "Sincronizando", "suspendEntity": "Suspender", "resumeEntity": "<PERSON><PERSON><PERSON>", "uninstallEntity": "<PERSON><PERSON><PERSON><PERSON>", "editEntity": "<PERSON><PERSON>", "confirmUninstallEntity": "Tem certeza que deseja desinstalar esta integração do GitHub? Is<PERSON> remover<PERSON> todos os dados associados a ela.", "entitySuspended": "Integração do GitHub suspensa com sucesso", "entityResumed": "Integração do GitHub reativada com sucesso", "entityUninstalled": "Integração do GitHub desinstalada com sucesso", "errorSuspendingEntity": "Falha ao suspender a integração do GitHub", "errorReactivatingEntity": "Falha ao reativar a integração do GitHub", "errorUninstallingEntity": "Falha ao desinstalar a integração do GitHub", "errorLoadingDetails": "Falha ao carregar detalhes da integração do GitHub", "configSaved": "Configurações do GitHub salvas com sucesso", "errorSavingConfig": "Falha ao salvar configurações do GitHub", "saving": "Salvando...", "addInstallation": "<PERSON><PERSON><PERSON><PERSON>", "installationAdded": "Instalação do GitHub adicionada com sucesso", "errorAddingInstallation": "Falha ao adicionar instalação do GitHub", "installedCount": "{count, plural, =0 {<PERSON><PERSON><PERSON>a instalação} =1 {1 instalação} other {# instalações}}", "action": {"title": "Instalação do GitHub", "subTitle": "Esta ação afetará sua integração com o GitHub para este espaço de trabalho.", "confirmationInstruction": "Para confirmar, digite o nome da conta \"{accountName}\" abaixo:", "executedSuccessfully": "Ação de instalação do GitHub executada com sucesso", "executedWithError": "Erro ao executar a ação de instalação do GitHub. Tente novamente mais tarde", "uninstalledDescription": "Isso removerá a integração com esta conta do GitHub deste espaço de trabalho e interromperá toda a sincronização de dados.", "suspendedDescription": "Isso suspenderá a integração com esta conta do GitHub para este espaço de trabalho e interromperá toda a sincronização de dados.", "activeDescription": "Isso retomará a integração com esta conta do GitHub para este espaço de trabalho e reiniciará toda a sincronização de dados.", "uninstalledWarning": "Se esta conta estiver conectada apenas a este espaço de trabalho, ela também será desinstalada do GitHub. Se estiver vinculada a outros espaços de trabalho do BMS Pulse, ele permanecerá ativo no GitHub.", "suspendedWarning": "Se esta conta estiver conectada apenas a este espaço de trabalho, ela também será suspensa no GitHub. Se estiver vinculada a outros espaços de trabalho do BMS Pulse, ela permanecerá ativa no GitHub.", "activeWarning": "Ela também será ativada no GitHub se estiver suspensa no momento. Outros espaços de trabalho vinculados à mesma conta do GitHub permanecerão suspensos.", "accountLoginMismatch": "O nome da conta não corresponde", "cancelButton": "<PERSON><PERSON><PERSON>", "loading": "Carregando...", "resumeButton": "<PERSON><PERSON><PERSON>", "suspendButton": "Suspender", "uninstallButton": "<PERSON><PERSON><PERSON><PERSON>", "result": {"goToGitHubSettings": "<PERSON><PERSON> <PERSON> as configurações do GitHub", "closeButton": "<PERSON><PERSON><PERSON>", "errorOnGitHub": "Ocorreu um erro na comunicação com o GitHub API.", "uninstallNotExecutedOnChannel": "A instalação foi removida com sucesso deste espaço de trabalho.\n\nNo entanto, como outros espaços de trabalho estão usando a mesma conta do GitHub, ela permanece vinculada ao BMS Pulse.", "uninstalledOnGitHubErrorDescription": "A instalação não está mais vinculada a este espaço de trabalho e não processaremos mais os dados desta integração.\n\nNo entanto, encontramos problemas ao desinstalá-la diretamente do GitHub. Para remover completamente o BMS Pulse da sua conta, visite o GitHub e confirme se a desinstalação foi bem-sucedida.", "suspendNotExecutedOnChannel": "A instalação foi suspensa com sucesso neste espaço de trabalho.\n\nNo entanto, como outros espaços de trabalho estão usando a mesma conta do GitHub, ela permanece ativa no GitHub.", "suspendOnGitHubErrorDescription": "A instalação foi suspensa neste espaço de trabalho e não processaremos mais os dados desta integração.\n\nNo entanto, encontramos problemas ao suspendê-la diretamente no GitHub. Se você também deseja suspendê-lo ou removê-lo do GitHub, acesse o GitHub diretamente e verifique se a ação foi bem-sucedida.", "withOtherWorkspaces": "Se você deseja que esta ação seja refletida no GitHub e em todos os espaços de trabalho vinculados, será necessário atualizar a instalação diretamente no GitHub ou executar esta ação em cada espaço de trabalho associado.\n\nObserve que isso afetará os seguintes espaços de trabalho:"}}}, "descriptions": {"github": "GitHub é um serviço de hospedagem baseado na web para controle de versão de código usando Git.", "jira": "Jira é um produto de rastreamento de problemas desenvolvido pela Atlas<PERSON> que permite o rastreamento de bugs e gerenciamento de projetos ágeis.", "slack": "Slack é um aplicativo de mensagens para empresas que conecta as pessoas às informações que precisam.", "azuredevops": "Azure DevOps fornece serviços de desenvolvedor para equipes de suporte planejarem o trabalho, colaborarem no desenvolvimento de código e construírem e implantarem aplicativos.", "bitbucket": "Bitbucket é um serviço de hospedagem de repositório de código-fonte baseado em Git, de propried<PERSON> <PERSON>.", "gitlab": "GitLab é uma ferramenta de ciclo de vida DevOps baseada na web que fornece um gerenciador de repositório Git com recursos de wiki, rastreamento de problemas e pipeline de CI/CD."}, "capabilities": {"pullRequests": "Análise de pull/merge requests", "codeReview": "Métricas de revisão de código", "commit": "<PERSON><PERSON><PERSON><PERSON>s", "repoInsights": "Insights de repositório", "teamPerformance": "Rastreamento de desempenho da equipe", "aiAnalytics": "Análise de ferramentas de IA", "issueTracking": "Integração de rastreamento de problemas", "sprintPerformance": "Métricas de desempenho de sprint", "backlogAnalysis": "<PERSON><PERSON><PERSON><PERSON>log", "projectTimeline": "Insights de linha do tempo do projeto", "teamWorkload": "Distribuição de carga de trabalho da equipe", "teamCommunication": "Análise de comunicação da equipe", "channelActivity": "Métricas de atividade de canal", "notifications": "Integração de notificações", "alertDistribution": "Distribuição de alertas", "collaborationInsights": "Insights de colaboração", "workItems": "Rastreamento de itens de trabalho", "repositoryAnalytics": "Análise de repositório", "cicdPipelines": "Análise de pipeline de CI/CD"}, "noResults": {"title": "Nenhuma integração encontrada", "description": "Nenhuma integração corresponde aos seus critérios de pesquisa. Tente ajustar os termos de pesquisa ou navegue por todas as integrações disponíveis.", "clearSearch": "<PERSON><PERSON> pes<PERSON>a"}, "errorState": {"title": "Falha ao carregar integrações", "description": "Não foi possível carregar as integrações do seu espaço de trabalho. Verifique sua conexão e tente novamente.", "retry": "Tentar novamente"}, "refresh": "<PERSON><PERSON><PERSON><PERSON>", "error": {"install": {"title": "Erro de instalação de integração", "description": "Ocorreu um erro ao instalar a extensão. Tente novamente mais tarde."}}, "dangerZone": {"description": "As ações nesta seção são irreversíveis e afetarão permanentemente sua integração.", "uninstallDescription": "Isso removerá permanentemente essa integração do seu espaço de trabalho e todas conexões ligadas à ela.\nOs dados associados não serão excluídos imediatamente, mas podem ser excluídos depois de um tempo.", "uninstallTitle": "Desinstalar integração", "title": "Zona de perigo"}}, "team": {"title": "Me<PERSON>ros da Equipe", "members": "Me<PERSON>ros da Equipe", "pendingInvitations": "<PERSON><PERSON><PERSON>", "noPendingInvitations": "Nenhum convite pendente", "inviteMember": "<PERSON><PERSON><PERSON>", "name": "Nome", "email": "Email", "role": "Função", "actions": "Ações", "edit": "<PERSON><PERSON>"}}, "git": {"overview": {"commitActivity": {"title": "Atividade de Commits", "today": "Hoje", "noActivity": "Sem atividade", "sync": "Sincronizar", "overview": "Visão geral"}, "actions": {"viewAll": "Ver tudo"}, "metrics": {"totalCommits": "Total de Commits", "pullRequests": "Pull Requests", "velocity": "Velocidade", "copilotMetrics": "Métricas do Copilot"}, "timeframes": {"sinceLastMonth": "<PERSON><PERSON> o mês passado", "lastDays": "{days, plural, =1 {Último dia} other {Últimos # dias}}"}, "status": {"open": "Abe<PERSON>o", "merged": "Mesclado", "closed": "<PERSON><PERSON><PERSON>", "unknown": "Desconhecido"}, "tableHeaders": {"prNumber": "PR #", "author": "Autor", "date": "Data", "status": "Status"}, "copilot": {"suggestions": "Sugestões", "acceptances": "Aceitações", "rejections": "Rejeições"}, "git": {"lastCommit": "Último commit: {date, date, medium} {date, time, short}"}, "titles": {"recentPullRequests": "Pull Requests Recentes", "recentRepositories": "Repositórios Recentes"}}, "repositories": {"title": "Repositories"}, "pullRequests": {"title": "Pull Requests"}, "commits": {"title": "Commits"}}, "customer": {"search": "Buscar cliente"}, "userPopup": {"account": "Conta", "security": "Segurança", "settings": "Configurações", "signOut": "<PERSON><PERSON>", "noDisplayName": "<PERSON><PERSON><PERSON><PERSON>"}, "insights": {"title": "Insights de Engenharia", "description": "Métricas-chave e respostas para as perguntas mais importantes para gerentes de engenharia.", "categories": {"deliveryDeadlines": "Entrega e Prazos", "qualityRisk": "Qualidade e Risco", "teamHealth": "Saúde da Equipe", "processEfficiency": "Eficiência de Processo", "businessValue": "Valor de Negócio", "benchmarksComparison": "Benchmarks e Comparação", "costResources": "Custo e Recursos"}, "questions": {"sprintDeadline": "Vamos cumprir a data da sprint / lançamento?", "blockingPRs": "Quais PRs estão bloqueando a implantação?", "pipelineBottleneck": "Qual equipe / módulo é o gargalo do pipeline?", "rework": "Quanto retrabalho (churn) tivemos esta semana?", "bugRate": "A taxa de bugs está aumentando após as últimas fusões?", "testCoverage": "Quão perto estamos da meta de cobertura de testes?", "burnout": "Alguém está à beira do esgotamento?", "workload": "Quem está sobrecarregado e quem está subutilizado?", "reviewTime": "Quanto tempo gastamos apenas esperando por revisão?", "sprintImprovement": "Estamos melhorando de sprint para sprint?", "techDebtVsFeatures": "Quanto esforço foi para recursos vs. dívida técnica?", "devROI": "Quais iniciativas trazem o maior ROI de desenvolvimento?", "marketComparison": "Estamos acima ou abaixo do percentil 50 do mercado para tempo de ciclo?", "internalBenchmark": "Qual equipe interna os outros devem espelhar?", "costPerEpic": "Qual é o custo por épico por sprint e está dentro do orçamento?", "cicdLostHours": "Quantas horas de CI/CD foram perdidas devido a falhas?"}, "metrics": {"burndownForecast": "Previsão de burndown", "percentDone": "% concluído vs. planejado", "openPRs": "PRs abertos > X h sem revisão", "cycleTime": "Tempo de ciclo por equipe e estágio", "reworkPercentage": "% linhas reescritas ≤ 30 dias", "incidentsPerKLOC": "Incidentes por KLOC / por lançamento", "coverageTarget": "Cobertura vs. meta", "consecutiveActiveHours": "Horas ativas consecutivas", "offHoursCommits": "Commits fora do horário", "workloadSpike": "Pico súbito de carga de trabalho", "wipPerDev": "WIP por desenvolvedor", "storyPointDistribution": "Distribuição de story points", "reviewLeadTime": "Tempo de espera de revisão", "prIdleTime": "Tempo ocioso de PR", "deltaCycleTime": "Δ Tempo de ciclo", "deltaLeadTime": "Δ Tempo de espera", "deltaDeployFreq": "Δ Freq. de implantação", "investmentProfile": "Perfil de investimento", "throughputVsOKR": "Throughput vs. impacto OKR", "anonymisedBenchmark": "Benchmark anonimizado", "internalHealthScore": "Classificação de saúde interna", "costPerStoryPoint": "Custo por story point", "buildFailMinutes": "<PERSON><PERSON><PERSON> de falha de build", "pipelineMTTR": "MTTR do pipeline"}, "actions": {"rescope": "Redefinir escopo ou adicionar mais pessoas", "notifyReviewers": "Notificar revisores; atribuir revisor automaticamente", "enablePairReview": "Habilitar revisão em pares ou trabalho paralelo", "startRootCause": "Iniciar an<PERSON><PERSON> de causa raiz / refatoração", "triggerQualityGate": "Acionar gate de qualidade, expandir testes", "createTestTasks": "<PERSON><PERSON>r tarefas para testes críticos", "schedule1on1": "Agendar 1-a-1, redistribuir tarefas", "rebalanceBacklog": "Reequilibrar backlog", "prSizePolicy": "Política de tamanho de PR, rotações de revisores", "adjustCeremonies": "Ajustar cerimônias / limite WIP", "defendRefactoringTime": "Defender tempo de refatoração", "prioritiseRoadmap": "Priorizar no roadmap", "shareBestPractices": "Compartilhar melhores práticas ou solicitar coaching", "createGuilds": "Criar guildas / dojos de aprendizado", "reviewEstimates": "<PERSON><PERSON><PERSON> estima<PERSON> ou contratar", "automateRollback": "Automatizar rollback, otimizar pipeline"}}, "common": {"title": "BMS Pulse"}, "errors": {"notFound": {"title": "404: A página que você está procurando não está aqui", "description": "Você tentou uma rota suspeita ou chegou aqui por engano. Seja qual for o caso, tente usar a navegação", "action": "Voltar para o início", "altText": "Em desenvolvimento"}}, "auth": {"guard": {"loadUserError": "Falha ao carregar dados do usuário", "checkOnboardingError": "Falha ao verificar status de onboarding", "retryingIn": "Tentando novamente em {seconds} segundos...", "retryAttempt": "Tentativa: {attempt} (<PERSON><PERSON><PERSON><PERSON> {backoff} segundos)", "retryAttemptSimple": "Tentativa: {attempt}", "authError": "Erro de autenticação", "notFoundError": "Recurso não encontrado", "networkError": "Erro de conexão de rede"}, "signIn": {"title": "Entrar", "emailLabel": "Email", "emailRequired": "Email é obrigatório", "passwordLabel": "<PERSON><PERSON>", "passwordRequired": "Senha é obrigatória", "submitButton": "Entrar", "noAccount": "Não tem uma conta?", "signUpLink": "Cadastre-se", "forgotPassword": "Esqueceu sua senha?"}, "signUp": {"title": "Cadastrar", "displayNameLabel": "Nome", "displayNameRequired": "Nome é obrigatório", "emailLabel": "Email", "emailRequired": "Email é obrigatório", "passwordLabel": "<PERSON><PERSON>", "passwordRequired": "A senha deve ter pelo menos 6 caracteres", "termsRequired": "Você deve aceitar os termos e condições", "submitButton": "Cadastrar", "haveAccount": "Já tem uma conta?", "signInLink": "Entrar"}, "resetPassword": {"title": "<PERSON><PERSON><PERSON><PERSON>", "emailLabel": "Email", "emailRequired": "Email é obrigatório", "submitButton": "Enviar link de recuperação", "backToSignIn": "Voltar para o login", "rememberPassword": "<PERSON><PERSON><PERSON> sua senha?", "signInLink": "Entrar", "successMessage": "Email de redefinição de senha enviado! Por favor, verifique sua caixa de entrada e siga as instruções."}, "errors": {"invalidCredentials": "<PERSON><PERSON> ou senha in<PERSON><PERSON>", "authFailed": "Falha na autenticação. Por favor, tente novamente.", "emailInUse": "Este email já está em uso. Por favor, use um email diferente ou faça login.", "weakPassword": "A senha é muito fraca. Por favor, use uma senha mais forte.", "registrationFailed": "Falha no registro. Por favor, verifique suas informações e tente novamente.", "resetFailed": "Falha ao enviar o email de redefinição de senha", "userNotFound": "Nenhuma conta encontrada com este endereço de email", "invalidEmail": "Formato de endereço de email inválido", "tooManyRequests": "Muitas solicitações. Por favor, tente novamente mais tarde"}}, "nav": {"home": "Início", "insights": "Insights", "delivery": "Entrega e Prazos", "quality": "Qualidade e Risco", "teamHealth": "Saúde da Equipe", "process": "Eficiência de Processo", "business": "Valor de Negócio", "benchmarks": "Benchmarks", "cost": "Custo e Recursos", "customers": "Clientes", "integrations": "Integrações", "settings": "Configurações", "account": "Conta", "workspace": "Espaço de trabalho", "search": "<PERSON><PERSON><PERSON><PERSON>", "contacts": "Contatos", "notifications": "Notificações", "analytics": "<PERSON><PERSON><PERSON><PERSON>", "git": "Git", "gitOverview": "Resumo", "repositories": "Repositórios", "pullRequests": "Pull Requests", "commits": "Commits"}, "layout": {"site": {"description": "Orientar as startups para construir ecossistemas de tecnologia escaláveis ​​e resilientes por meio de estratégias acionáveis ​​e experiência no mundo real."}, "welcome": "Bem-vindo a"}, "onboarding": {"steps": {"welcome": "<PERSON><PERSON>-vindo", "userDetails": "<PERSON><PERSON>", "preferences": "Preferências"}, "buttons": {"back": "Voltar", "next": "Próximo", "complete": "Concluir", "saving": "Salvando...", "redirecting": "Redirecionando...", "getStarted": "<PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON>"}, "errors": {"submitFailed": "Falha ao concluir o onboarding. Por favor, tente novamente.", "requiredFields": "Por favor, preencha todos os campos obrigatórios antes de prosseguir."}, "welcome": {"title": "Bem-vindo ao BMS Pulse", "description": "Estamos animados em tê-lo a bordo! Vamos configurar sua conta para aproveitar ao máximo o BMS Pulse.", "imageAlt": "Ilustração de boas-vindas"}, "userDetails": {"title": "Conte-nos sobre você", "description": "Essas informações nos ajudam a personalizar sua experiência.", "displayName": "Nome", "email": "Endereço de email", "phone": "Número de telefone (opcional)", "invalidPhone": "Por favor, insira um número de telefone válido", "countryLabel": "<PERSON><PERSON>", "timezone": "<PERSON><PERSON>", "displayNameRequired": "Nome é obrigatório", "countries": {"us": "Estados Unidos", "br": "Brasil", "gb": "Reino Unido", "ca": "Canadá", "jp": "Japão"}, "countryRequired": "O país é obrigatório", "timezoneRequired": "O fuso horário é obrigatório", "maxNameSize": "O nome deve ser menor que {size} caracteres"}, "preferences": {"title": "Defina suas preferências", "description": "Personalize sua experiência com o BMS Pulse."}, "workspace": {"title": "Crie seu espaço de trabalho", "description": "Seu espaço de trabalho é onde você gerenciará seus projetos e equipe.", "workspaceName": "Nome do espaço de trabalho", "workspaceNamePlaceholder": "<PERSON><PERSON> Empresa", "workspaceNameRequired": "Nome do espaço de trabalho é obrigatório", "workspaceNote": "Você pode adicionar membros da equipe e configurar seu espaço de trabalho mais tarde."}, "completion": {"title": "Tudo pronto!", "description": "Sua conta agora está pronta para uso. Em seguida, você precisará criar ou selecionar um espaço de trabalho para começar.", "imageAlt": "Ilustração de configuração concluída", "redirectMessage": "Redirecionando para seleção de espaço de trabalho em {seconds} segundos...", "processing": "Processando...", "redirecting": "Redirecionando para seleção de espaço de trabalho..."}}, "workspace": {"selection": {"title": "Selecione um espaço de trabalho", "description": "Escolha um espaço de trabalho para continuar ou crie um novo.", "noWorkspaces": "Nenhum espaço de trabalho encontrado", "noWorkspacesDescription": "Você ainda não tem acesso a nenhum espaço de trabalho. Crie seu primeiro espaço de trabalho para começar.", "createWorkspace": "Criar espaço de trabalho", "selectWorkspace": "Selecionar espaço de trabalho", "workspaceName": "Nome do espaço de trabalho", "workspaceNamePlaceholder": "<PERSON><PERSON> Empresa", "workspaceNameRequired": "Nome do espaço de trabalho é obrigatório", "createButton": "<PERSON><PERSON><PERSON>", "cancelButton": "<PERSON><PERSON><PERSON>", "creating": "Criando...", "selecting": "Se<PERSON><PERSON>and<PERSON>...", "createWorkspaceTitle": "Criar novo espaço de trabalho", "createWorkspaceDescription": "Digite um nome para seu novo espaço de trabalho.", "loadingWorkspaces": "Carregando espaços de trabalho...", "searchWorkspaces": "Buscar espaços de trabalho...", "noSearchResults": "Nenhum espaço de trabalho encontrado", "noSearchResultsDescription": "Tente ajustar seus termos de busca ou crie um novo espaço de trabalho.", "editWorkspace": "<PERSON>ar <PERSON> de Trabalho", "deleteWorkspace": "Excluir Espaço de Trabalho", "editWorkspaceTitle": "<PERSON>ar <PERSON> de Trabalho", "editWorkspaceDescription": "Atualize os detalhes do seu espaço de trabalho", "deleteWorkspaceTitle": "Excluir Espaço de Trabalho", "deleteWorkspaceDescription": "Esta ação não pode ser desfeita", "workspaceAvatar": "Avatar do Espaço de Trabalho", "uploadAvatarDescription": "Clique para fazer upload de um avatar para seu espaço de trabalho", "avatarRequirements": "PNG, JPG até 2MB", "changeAvatar": "Alterar <PERSON>", "saveChanges": "<PERSON><PERSON>", "saving": "Salvando...", "deleting": "Excluindo...", "deleteConfirmation": "Tem certeza de que deseja excluir", "deleteWarning": "Todos os dados associados a este espaço de trabalho serão permanentemente excluídos.", "deleteButton": "Excluir", "createWorkspaceCardDescription": "Crie um novo espaço de trabalho para começar", "errors": {"createFailed": "Falha ao criar espaço de trabalho. Tente novamente.", "selectFailed": "Falha ao selecionar espaço de trabalho. Tente novamente.", "loadFailed": "Falha ao carregar espaços de trabalho. Tente novamente.", "editFailed": "Falha ao atualizar espaço de trabalho. Tente novamente.", "deleteFailed": "Falha ao excluir espaço de trabalho. Tente novamente."}}, "invitations": {"title": "<PERSON><PERSON><PERSON>", "loading": "Carregando convites...", "noInvitations": "Nenhum convite pendente", "noInvitationsDescription": "Você não tem convites pendentes para espaços de trabalho no momento.", "invitedBy": "Convidado por {name}", "accept": "Aceitar", "decline": "Recusar", "accepting": "Aceitando...", "declining": "Recusando...", "invitation": {"title": "Convite para Espaço de Trabalho", "error": "<PERSON>rro no Convite", "success": "<PERSON><PERSON><PERSON>"}, "pageTitle": "Convite para Espaço de Trabalho", "pageDescription": "Aceite seu convite para o espaço de trabalho", "description": "Você foi convidado para participar destes espaços de trabalho", "pending": "Pendente", "labels": {"role": "Função", "invitedBy": "Convidado por"}, "messages": {"invitedTo": "Você foi convidado para participar de {workspace}", "signInRequired": "Por favor, faça login ou crie uma conta para aceitar este convite", "acceptedSuccess": "Você se juntou com sucesso a {workspace}!", "redirecting": "Redirecionando você para o espaço de trabalho..."}, "actions": {"acceptInvitation": "Aceitar Convite", "accepting": "Aceitando...", "signIn": "Entrar", "signUp": "<PERSON><PERSON><PERSON>", "goHome": "Ir para Início"}, "errors": {"notFound": "Este link de convite é inválido ou expirou", "alreadyProcessed": "Este convite já foi aceito ou recusado", "loadFailed": "Falha ao carregar detalhes do convite", "acceptFailed": "Falha ao aceitar convite. Tente novamente.", "emailMismatch": "Este convite é para um endereço de email diferente", "wrongAccount": "Você está logado como {currentEmail}, mas este convite é para {invitedEmail}. Por favor, faça login com a conta correta."}}}, "home": {"onboarding": {"welcome": {"title": "Bem-vindo ao BMS Pulse", "subtitle": "Sua plataforma de análise de engenharia para ajudá-lo a tomar decisões baseadas em dados e melhorar seu processo de desenvolvimento."}, "getStarted": {"title": "Comece com o BMS Pulse", "subtitle": "Complete estas etapas para configurar seu espaço de trabalho e começar a obter insights"}, "steps": {"gitIntegration": {"title": "Conectar Repositório Git", "description": "Conecte seus repositórios GitHub, GitLab ou Bitbucket para começar a rastrear suas métricas de desenvolvimento."}, "exploreInsights": {"title": "Explorar Insights", "description": "Descubra insights acionáveis sobre seu processo de desenvolvimento, desempenho da equipe e qualidade do código."}, "customizeSettings": {"title": "Personalize sua Experiência", "description": "Configure suas preferências, notificações e membros da equipe para adaptar o BMS Pulse às suas necessidades."}}, "quickStart": {"title": "<PERSON><PERSON><PERSON> <PERSON> Início <PERSON>", "subtitle": "Me<PERSON>ulhe e explore a plataforma", "description": "O BMS Pulse fornece análises e insights em tempo real para sua equipe de desenvolvimento. Comece explorando o painel de Visão Geral do Git para ver a atividade da sua equipe em um relance.", "button": "<PERSON>r para o <PERSON>", "imageAlt": "Prévia do painel"}, "common": {"getStarted": "<PERSON><PERSON><PERSON>", "learnMore": "<PERSON><PERSON> Mai<PERSON>", "skip": "<PERSON><PERSON> por enquanto"}}}, "landing": {"header": {"termsOfService": "Termos de serviço", "privacyPolicy": "Política de privacidade", "bookDemo": "Agendar demo"}, "badge": {"backedBy": "<PERSON><PERSON><PERSON><PERSON> pela", "yCombinator": "Y Combinator", "trustedBy": ""}, "hero": {"title": "Pulse – Clareza de Engenharia em", "titleHighlight": "Um Dashboard", "subtitle": "Agentes alimentados por IA personalizam métricas e insights preditivos para CTOs e gerentes de engenharia, transformando dados brutos de desenvolvedores em ação decisiva.", "bookDemo": "Agendar demo", "signIn": "Entrar", "enterApp": "Acessar App"}, "mockup": {"projectTitle": "⚠️ Crítico: Velocidade do sprint caiu 25%", "projectSubtitle": "IA detectou gargalo no processo de revisão de código - ação imediata necessária", "analyzingStack": "<PERSON><PERSON><PERSON><PERSON> stack"}, "footer": {"copyright": "© 2025 BMS Pulse. Todos os direitos reservados.", "tagline": "Transforme suas análises de desenvolvimento"}}}