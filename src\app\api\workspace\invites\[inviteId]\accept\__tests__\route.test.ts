/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server';

import { PATCH } from '../route';

// Mock dependencies
jest.mock('@/services/firebase/admin', () => ({
  adminAuth: {
    verifyIdToken: jest.fn(),
  },
}));

jest.mock('@/services/db', () => ({
  db: {
    workspaceInvite: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
    workspaceMembership: {
      findFirst: jest.fn(),
      create: jest.fn(),
    },
    role: {
      findFirst: jest.fn(),
    },
    $transaction: jest.fn(),
  },
}));

jest.mock('@/services/firebase/server-app', () => ({
  getAuthenticatedAppForUser: jest.fn(),
}));

// Import mocked modules
import { adminAuth } from '@/services/firebase/admin';
import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

const mockAdminAuth = adminAuth as jest.Mocked<typeof adminAuth>;
const mockDb = db as jest.Mocked<typeof db>;
const mockGetAuthenticatedAppForUser = getAuthenticatedAppForUser as jest.MockedFunction<typeof getAuthenticatedAppForUser>;

// Helper function to create mock request
function createMockRequest(method: string, token?: string): NextRequest {
  const url = 'http://localhost:3000/api/workspace/invites/invite-1/accept';
  const headers = new Headers();
  if (token) {
    headers.set('Authorization', `Bearer ${token}`);
  }
  headers.set('Content-Type', 'application/json');

  return new NextRequest(url, {
    method,
    headers,
  });
}

// Mock data
const mockUser = {
  uid: 'user-1',
  email: '<EMAIL>',
  displayName: 'Test User',
};

const mockInvitation = {
  id: 'invite-1',
  email: '<EMAIL>',
  workspaceId: 'workspace-1',
  invitedBy: 'inviter-1',
  userId: 'user-1',
  roleId: 'member',
  status: 'PENDING',
  createdAt: new Date(),
  updatedAt: new Date(),
  token: null,
};

const mockRole = {
  id: 'member',
  name: 'Member',
  workspaceId: 'workspace-1',
};

const mockMembership = {
  id: 'membership-1',
  userId: 'user-1',
  workspaceId: 'workspace-1',
  roleId: 'member',
  createdAt: new Date(),
  updatedAt: new Date(),
};

describe('/api/workspace/invites/[inviteId]/accept', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('PATCH', () => {
    it('should accept invitation successfully', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(mockInvitation as any);
      mockDb.workspaceMembership.findFirst.mockResolvedValue(null); // Not already a member
      mockDb.role.findFirst.mockResolvedValue(mockRole as any);
      
      // Mock transaction
      mockDb.$transaction.mockImplementation(async (callback) => {
        return await callback({
          workspaceInvite: {
            update: jest.fn().mockResolvedValue(mockInvitation),
          },
          workspaceMembership: {
            create: jest.fn().mockResolvedValue(mockMembership),
          },
        } as any);
      });

      const request = createMockRequest('PATCH', 'valid-token');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Invitation accepted successfully');
      expect(data.membership).toEqual({
        ...mockMembership,
        createdAt: mockMembership.createdAt.toISOString(),
        updatedAt: mockMembership.updatedAt.toISOString(),
      });
    });

    it('should return 401 for unauthenticated user', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue(null as any);

      const request = createMockRequest('PATCH');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(401);
    });

    it('should return 404 for non-existent invitation', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(null);

      const request = createMockRequest('PATCH', 'valid-token');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invalid-invite' }) });

      expect(response.status).toBe(400);
    });

    it('should return 400 for already accepted invitation', async () => {
      const acceptedInvitation = { ...mockInvitation, status: 'ACCEPTED' };
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(acceptedInvitation as any);

      const request = createMockRequest('PATCH', 'valid-token');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(400);
    });

    it('should return 400 for invitation without roleId', async () => {
      const invitationWithoutRole = { ...mockInvitation, roleId: null };
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(invitationWithoutRole as any);

      const request = createMockRequest('PATCH', 'valid-token');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(400);
    });

    it('should return 404 for invalid role', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(mockInvitation as any);
      mockDb.workspaceMembership.findFirst.mockResolvedValue(null);
      mockDb.role.findFirst.mockResolvedValue(null); // Role not found

      const request = createMockRequest('PATCH', 'valid-token');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(404);
    });

    it('should return 400 for user already a member', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(mockInvitation as any);
      mockDb.workspaceMembership.findFirst.mockResolvedValue(mockMembership as any); // Already a member
      mockDb.role.findFirst.mockResolvedValue(mockRole as any);

      const request = createMockRequest('PATCH', 'valid-token');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(400);
    });

    it('should return 403 for unauthorized user (different email)', async () => {
      const differentUser = { ...mockUser, uid: 'different-user', email: '<EMAIL>' };
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: differentUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(mockInvitation as any);
      mockDb.workspaceMembership.findFirst.mockResolvedValue(null); // Not already a member

      const request = createMockRequest('PATCH', 'valid-token');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(403);
    });

    it('should handle transaction errors', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(mockInvitation as any);
      mockDb.workspaceMembership.findFirst.mockResolvedValue(null);
      mockDb.role.findFirst.mockResolvedValue(mockRole as any);
      
      // Mock transaction failure
      mockDb.$transaction.mockRejectedValue(new Error('Transaction failed'));

      const request = createMockRequest('PATCH', 'valid-token');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(500);
    });
  });
});
