import type { <PERSON>ada<PERSON> } from 'next';
import * as React from 'react';

import { config } from '@/config';
import { TeamSettingsClient } from '@/components/settings/team/team-settings-client';

export const metadata = {
  title: `Team | Settings | ${config.site.name}`,
} satisfies Metadata;

export default async function Page(): Promise<React.JSX.Element> {
  return <TeamSettingsClient />;
}
