/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server';

import { GET } from '../route';
import { db } from '@/services/db';

// Mock the database
jest.mock('@/services/db', () => ({
  db: {
    workspaceInvite: {
      findFirst: jest.fn(),
    },
  },
}));

const mockDb = db as jest.Mocked<typeof db>;

describe('/api/invites/token/[token] - GET', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const mockInvitation = {
    id: 'invite-1',
    email: '<EMAIL>',
    status: 'PENDING',
    workspace: {
      id: 'workspace-1',
      name: 'Test Workspace',
      description: 'A test workspace',
    },
    role: {
      id: 'role-1',
      name: 'Member',
    },
    invitedByUser: {
      id: 'user-1',
      email: '<EMAIL>',
      displayName: '<PERSON>',
    },
    createdAt: new Date('2024-01-01T00:00:00Z'),
  };

  it('should return invitation details for valid token', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
    mockDb.workspaceInvite.findFirst.mockResolvedValue(mockInvitation as any);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test');
    const params = Promise.resolve({ token: validToken });
    
    const response = await GET(request, { params });
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toEqual({
      id: mockInvitation.id,
      email: mockInvitation.email,
      status: mockInvitation.status,
      workspace: mockInvitation.workspace,
      role: mockInvitation.role,
      invitedByUser: mockInvitation.invitedByUser,
      createdAt: mockInvitation.createdAt.toISOString(),
    });
    expect(mockDb.workspaceInvite.findFirst).toHaveBeenCalledWith({
      where: { token: validToken },
      include: {
        workspace: { select: { id: true, name: true, description: true } },
        role: { select: { id: true, name: true } },
        invitedByUser: { select: { id: true, email: true, displayName: true } },
      },
    });
  });

  it('should return 400 for invalid token format', async () => {
    const invalidToken = 'invalid-token';
    
    const request = new NextRequest('http://localhost:3000/api/invites/token/test');
    const params = Promise.resolve({ token: invalidToken });
    
    const response = await GET(request, { params });
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error).toBe('Invalid token format');
    expect(mockDb.workspaceInvite.findFirst).not.toHaveBeenCalled();
  });

  it('should return 404 for non-existent invitation', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
    mockDb.workspaceInvite.findFirst.mockResolvedValue(null);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test');
    const params = Promise.resolve({ token: validToken });
    
    const response = await GET(request, { params });
    const data = await response.json();

    expect(response.status).toBe(404);
    expect(data.error).toBe('Invitation not found or expired');
  });

  it('should return 410 for already processed invitation', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
    const processedInvitation = { ...mockInvitation, status: 'ACCEPTED' };
    mockDb.workspaceInvite.findFirst.mockResolvedValue(processedInvitation as any);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test');
    const params = Promise.resolve({ token: validToken });
    
    const response = await GET(request, { params });
    const data = await response.json();

    expect(response.status).toBe(410);
    expect(data.error).toBe('Invitation already processed');
    expect(data.status).toBe('ACCEPTED');
  });

  it('should handle database errors', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
    mockDb.workspaceInvite.findFirst.mockRejectedValue(new Error('Database error'));

    const request = new NextRequest('http://localhost:3000/api/invites/token/test');
    const params = Promise.resolve({ token: validToken });
    
    const response = await GET(request, { params });
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe('Failed to retrieve invitation');
  });
});
