'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Stack,
  Typography,
  Avatar,
  Chip,
} from '@mui/material';
import { Check, X, Mail, Business } from '@phosphor-icons/react';

import { useAuth } from '@/contexts/firebase-auth-context';
import { paths } from '@/paths';

interface InvitationDetails {
  id: string;
  email: string;
  status: string;
  workspace: {
    id: string;
    name: string;
    description?: string;
  };
  role: {
    id: string;
    name: string;
  };
  invitedByUser: {
    id: string;
    email: string;
    displayName?: string;
  };
  createdAt: string;
}

interface InvitationHandlerProps {
  token: string;
}

export function InvitationHandler({ token }: InvitationHandlerProps) {
  const t = useTranslations('invitations');
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();

  const [invitation, setInvitation] = useState<InvitationDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [accepting, setAccepting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Fetch invitation details
  useEffect(() => {
    const fetchInvitation = async () => {
      try {
        const response = await fetch(`/api/invites/token/${token}`);
        
        if (response.ok) {
          const data = await response.json();
          setInvitation(data);
        } else if (response.status === 404) {
          setError(t('errors.notFound'));
        } else if (response.status === 410) {
          setError(t('errors.alreadyProcessed'));
        } else {
          setError(t('errors.loadFailed'));
        }
      } catch (err) {
        console.error('Error fetching invitation:', err);
        setError(t('errors.loadFailed'));
      } finally {
        setLoading(false);
      }
    };

    fetchInvitation();
  }, [token, t]);

  const handleAcceptInvitation = async () => {
    if (!user) {
      // Redirect to sign in with return URL
      const returnUrl = `/invite/${token}`;
      router.push(`${paths.auth.signIn}?returnTo=${encodeURIComponent(returnUrl)}`);
      return;
    }

    setAccepting(true);
    setError(null);

    try {
      const response = await fetch(`/api/invites/token/${token}/accept`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await user.getIdToken()}`,
        },
      });

      if (response.ok) {
        setSuccess(true);
        // Redirect to workspace after a short delay
        setTimeout(() => {
          router.push(paths.root);
        }, 2000);
      } else {
        const errorData = await response.json();
        if (response.status === 403) {
          setError(t('errors.emailMismatch'));
        } else if (response.status === 400) {
          setError(errorData.error || t('errors.acceptFailed'));
        } else {
          setError(t('errors.acceptFailed'));
        }
      }
    } catch (err) {
      console.error('Error accepting invitation:', err);
      setError(t('errors.acceptFailed'));
    } finally {
      setAccepting(false);
    }
  };

  const handleSignIn = () => {
    const returnUrl = `/invite/${token}`;
    router.push(`${paths.auth.signIn}?returnTo=${encodeURIComponent(returnUrl)}`);
  };

  const handleSignUp = () => {
    const returnUrl = `/invite/${token}`;
    router.push(`${paths.auth.signUp}?returnTo=${encodeURIComponent(returnUrl)}`);
  };

  if (authLoading || loading) {
    return (
      <Container maxWidth="sm" sx={{ py: 8 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="sm" sx={{ py: 8 }}>
        <Card>
          <CardContent>
            <Stack spacing={3} alignItems="center">
              <X size={48} color="error" />
              <Typography variant="h5" textAlign="center">
                {t('title.error')}
              </Typography>
              <Alert severity="error">{error}</Alert>
              <Button variant="outlined" onClick={() => router.push(paths.root)}>
                {t('actions.goHome')}
              </Button>
            </Stack>
          </CardContent>
        </Card>
      </Container>
    );
  }

  if (success) {
    return (
      <Container maxWidth="sm" sx={{ py: 8 }}>
        <Card>
          <CardContent>
            <Stack spacing={3} alignItems="center">
              <Check size={48} color="success" />
              <Typography variant="h5" textAlign="center">
                {t('title.success')}
              </Typography>
              <Alert severity="success">
                {t('messages.acceptedSuccess', { workspace: invitation?.workspace.name })}
              </Alert>
              <Typography variant="body2" color="text.secondary" textAlign="center">
                {t('messages.redirecting')}
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      </Container>
    );
  }

  if (!invitation) {
    return null;
  }

  return (
    <Container maxWidth="sm" sx={{ py: 8 }}>
      <Card>
        <CardContent>
          <Stack spacing={4}>
            {/* Header */}
            <Stack spacing={2} alignItems="center">
              <Mail size={48} />
              <Typography variant="h4" textAlign="center">
                {t('title.invitation')}
              </Typography>
              <Typography variant="body1" color="text.secondary" textAlign="center">
                {t('messages.invitedTo', { workspace: invitation.workspace.name })}
              </Typography>
            </Stack>

            {/* Invitation Details */}
            <Stack spacing={3}>
              <Box>
                <Typography variant="h6" gutterBottom>
                  <Business size={20} style={{ verticalAlign: 'middle', marginRight: 8 }} />
                  {invitation.workspace.name}
                </Typography>
                {invitation.workspace.description && (
                  <Typography variant="body2" color="text.secondary">
                    {invitation.workspace.description}
                  </Typography>
                )}
              </Box>

              <Stack direction="row" spacing={2} alignItems="center">
                <Typography variant="body2" color="text.secondary">
                  {t('labels.role')}:
                </Typography>
                <Chip label={invitation.role.name} color="primary" variant="outlined" size="small" />
              </Stack>

              <Stack direction="row" spacing={2} alignItems="center">
                <Typography variant="body2" color="text.secondary">
                  {t('labels.invitedBy')}:
                </Typography>
                <Stack direction="row" spacing={1} alignItems="center">
                  <Avatar sx={{ width: 24, height: 24 }}>
                    {invitation.invitedByUser.displayName?.[0] || invitation.invitedByUser.email[0]}
                  </Avatar>
                  <Typography variant="body2">
                    {invitation.invitedByUser.displayName || invitation.invitedByUser.email}
                  </Typography>
                </Stack>
              </Stack>
            </Stack>

            {/* Actions */}
            {user ? (
              <Stack spacing={2}>
                {user.email === invitation.email ? (
                  <Button
                    variant="contained"
                    size="large"
                    onClick={handleAcceptInvitation}
                    disabled={accepting}
                    startIcon={accepting ? <CircularProgress size={20} /> : <Check />}
                  >
                    {accepting ? t('actions.accepting') : t('actions.acceptInvitation')}
                  </Button>
                ) : (
                  <Alert severity="warning">
                    {t('errors.wrongAccount', { 
                      currentEmail: user.email, 
                      invitedEmail: invitation.email 
                    })}
                  </Alert>
                )}
              </Stack>
            ) : (
              <Stack spacing={2}>
                <Alert severity="info">
                  {t('messages.signInRequired')}
                </Alert>
                <Stack direction="row" spacing={2}>
                  <Button variant="contained" onClick={handleSignIn} fullWidth>
                    {t('actions.signIn')}
                  </Button>
                  <Button variant="outlined" onClick={handleSignUp} fullWidth>
                    {t('actions.signUp')}
                  </Button>
                </Stack>
              </Stack>
            )}
          </Stack>
        </CardContent>
      </Card>
    </Container>
  );
}
