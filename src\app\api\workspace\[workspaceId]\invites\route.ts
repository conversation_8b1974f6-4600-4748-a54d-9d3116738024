import 'server-only';

import { Permission } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { z as zod } from 'zod';

import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

// Schema for invitation creation
const createInviteSchema = zod.object({
  email: zod.string().email('Invalid email address'),
  roleId: zod.string().min(1, 'Role ID is required'),
});

/**
 * @swagger
 * /api/workspace/{workspaceId}/invites:
 *   get:
 *     summary: Get workspace invitations
 *     description: Retrieve all invitations for a specific workspace. Only users with INVITE_MEMBER permission can access this endpoint.
 *     tags:
 *       - Workspace
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: The workspace ID
 *     responses:
 *       200:
 *         description: Successfully retrieved workspace invitations
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   email:
 *                     type: string
 *                   status:
 *                     type: string
 *                     enum: [PENDING, ACCEPTED, REJECTED]
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *                   updatedAt:
 *                     type: string
 *                     format: date-time
 *                   workspaceId:
 *                     type: string
 *                   invitedBy:
 *                     type: string
 *                   userId:
 *                     type: string
 *                     nullable: true
 *                   invitedByUser:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       email:
 *                         type: string
 *                       displayName:
 *                         type: string
 *                   user:
 *                     type: object
 *                     nullable: true
 *                     properties:
 *                       id:
 *                         type: string
 *                       email:
 *                         type: string
 *                       displayName:
 *                         type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have INVITE_MEMBER permission
 *       500:
 *         description: Internal server error
 *   post:
 *     summary: Create workspace invitation
 *     description: Create a new invitation to join a workspace. Only users with INVITE_MEMBER permission can create invitations.
 *     tags:
 *       - Workspace
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: The workspace ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email address of the user to invite
 *               roleId:
 *                 type: string
 *                 description: Role ID to assign to the invited user
 *             required:
 *               - email
 *               - roleId
 *     responses:
 *       201:
 *         description: Invitation created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 email:
 *                   type: string
 *                 status:
 *                   type: string
 *                   enum: [PENDING, ACCEPTED, REJECTED]
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                 workspaceId:
 *                   type: string
 *                 invitedBy:
 *                   type: string
 *                 userId:
 *                   type: string
 *                   nullable: true
 *       400:
 *         description: Bad request - invalid input or user already invited/member
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have INVITE_MEMBER permission
 *       500:
 *         description: Internal server error
 */
export async function POST(request: NextRequest, props: { params: Promise<{ workspaceId: string }> }) {
  const params = await props.params;
  try {
    const { workspaceId } = params;

    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = authResult.currentUser;

    // Check if the user has permission to invite members
    let membership;
    try {
      membership = await db.workspaceMembership.findFirst({
        where: {
          userId: currentUser.uid,
          workspaceId: workspaceId,
          role: {
            permissions: {
              some: {
                permission: {
                  id: Permission.INVITE_MEMBER,
                },
              },
            },
          },
        },
      });
    } catch (error) {
      console.error('Error checking permissions:', error);
      return NextResponse.json({ error: 'Failed to check permissions' }, { status: 500 });
    }

    if (!membership) {
      return NextResponse.json({ error: 'You do not have permission to invite members to this workspace' }, { status: 403 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = createInviteSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json({ error: 'Invalid request', details: validationResult.error.format() }, { status: 400 });
    }

    const { email, roleId } = validationResult.data;

    // Check if the role exists and belongs to this workspace or is a global role
    const role = await db.role.findFirst({
      where: {
        id: roleId,
        OR: [
          { workspaceId: workspaceId },
          { workspaceId: null }, // Global roles
        ],
      },
    });

    if (!role) {
      return NextResponse.json({ error: 'Invalid role specified' }, { status: 400 });
    }

    // Check if user is already a member of the workspace
    const existingMembership = await db.workspaceMembership.findFirst({
      where: {
        workspaceId: workspaceId,
        user: {
          email: email,
        },
      },
    });

    if (existingMembership) {
      return NextResponse.json({ error: 'User is already a member of this workspace' }, { status: 400 });
    }

    // Check if there's already a pending invitation for this email
    const existingInvite = await db.workspaceInvite.findFirst({
      where: {
        workspaceId: workspaceId,
        email: email,
        status: 'PENDING',
      },
    });

    if (existingInvite) {
      return NextResponse.json({ error: 'User already has a pending invitation to this workspace' }, { status: 400 });
    }

    // Check if the user exists in the system
    const existingUser = await db.user.findFirst({
      where: {
        email: email,
      },
    });

    // Create the invitation
    const invitation = await db.workspaceInvite.create({
      data: {
        email: email,
        workspaceId: workspaceId,
        invitedBy: currentUser.uid,
        userId: existingUser?.id || null,
        roleId: roleId,
        status: 'PENDING',
        token: null, // Token generation can be added later for email links
      },
    });

    return NextResponse.json(invitation, { status: 201 });
  } catch (error) {
    console.error('Error creating workspace invitation:', error);
    return NextResponse.json({ error: 'Failed to create invitation' }, { status: 500 });
  }
}

/**
 * GET /api/workspace/{workspaceId}/invites
 * Get all invitations for a workspace
 */
export async function GET(request: NextRequest, props: { params: Promise<{ workspaceId: string }> }) {
  const params = await props.params;
  try {
    const { workspaceId } = params;

    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = authResult.currentUser;

    // Check if the user has permission to view invitations (same as invite permission)
    const membership = await db.workspaceMembership.findFirst({
      where: {
        userId: currentUser.uid,
        workspaceId: workspaceId,
        role: {
          permissions: {
            some: {
              id: Permission.INVITE_MEMBER,
            },
          },
        },
      },
    });

    if (!membership) {
      return NextResponse.json({ error: 'You do not have permission to view invitations for this workspace' }, { status: 403 });
    }

    // Get all invitations for the workspace
    const invitations = await db.workspaceInvite.findMany({
      where: {
        workspaceId: workspaceId,
      },
      include: {
        invitedByUser: {
          select: {
            id: true,
            email: true,
            displayName: true,
          },
        },
        user: {
          select: {
            id: true,
            email: true,
            displayName: true,
          },
        },
        role: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json(invitations);
  } catch (error) {
    console.error('Error getting workspace invitations:', error);
    return NextResponse.json({ error: 'Failed to get invitations' }, { status: 500 });
  }
}
