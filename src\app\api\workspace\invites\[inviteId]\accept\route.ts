import 'server-only';

import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

/**
 * @swagger
 * /api/workspace/invites/{inviteId}/accept:
 *   patch:
 *     summary: Accept workspace invitation
 *     description: Accept a pending workspace invitation. Only the invited user can accept their own invitation.
 *     tags:
 *       - Workspace
 *     parameters:
 *       - in: path
 *         name: inviteId
 *         required: true
 *         schema:
 *           type: string
 *         description: The invitation ID
 *     responses:
 *       200:
 *         description: Invitation accepted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Invitation accepted successfully"
 *                 membership:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                     workspaceId:
 *                       type: string
 *                     roleId:
 *                       type: string
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: Bad request - invitation not found, already processed, or invalid
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user cannot accept this invitation
 *       500:
 *         description: Internal server error
 */
export async function PATCH(request: NextRequest, props: { params: Promise<{ inviteId: string }> }) {
  const params = await props.params;
  try {
    const { inviteId } = params;

    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = authResult.currentUser;

    // Get the invitation
    const invitation = await db.workspaceInvite.findUnique({
      where: {
        id: inviteId,
      },
      include: {
        workspace: true,
      },
    });

    if (!invitation) {
      return NextResponse.json({ error: 'Invitation not found' }, { status: 400 });
    }

    // Check if invitation is still pending
    if (invitation.status !== 'PENDING') {
      return NextResponse.json({ error: 'Invitation has already been processed' }, { status: 400 });
    }

    // Check if the current user can accept this invitation
    // They can accept if:
    // 1. The invitation has their userId (they're already in the system), OR
    // 2. The invitation email matches their email (they registered after being invited)
    const canAccept = 
      (invitation.userId && invitation.userId === currentUser.uid) ||
      (invitation.email && invitation.email === currentUser.email);

    if (!canAccept) {
      return NextResponse.json({ error: 'You cannot accept this invitation' }, { status: 403 });
    }

    // Check if user is already a member of the workspace
    const existingMembership = await db.workspaceMembership.findFirst({
      where: {
        userId: currentUser.uid,
        workspaceId: invitation.workspaceId!,
      },
    });

    if (existingMembership) {
      // Update invitation status to accepted even though they're already a member
      await db.workspaceInvite.update({
        where: { id: inviteId },
        data: { status: 'ACCEPTED' },
      });
      return NextResponse.json({ error: 'You are already a member of this workspace' }, { status: 400 });
    }

    // For accepting invitations, we need to determine the role from the invitation
    // Since we don't store roleId in the invitation yet, we'll use a default logic
    // In a real implementation, the invitation should store the intended roleId
    const memberCount = await db.workspaceMembership.count({
      where: { workspaceId: invitation.workspaceId! },
    });

    // Get appropriate role - if it's the first member, make them owner, otherwise member
    let roleId = 'member'; // Default role
    if (memberCount === 0) {
      roleId = 'owner';
    }

    // Check if the role exists
    const role = await db.role.findFirst({
      where: {
        id: roleId,
        OR: [
          { workspaceId: invitation.workspaceId },
          { workspaceId: null }, // Global roles
        ],
      },
    });

    if (!role) {
      // Fallback to owner role if member role doesn't exist
      const ownerRole = await db.role.findFirst({
        where: { id: 'owner' },
      });
      if (ownerRole) {
        roleId = 'owner';
      } else {
        return NextResponse.json({ error: 'No valid role found for invitation' }, { status: 500 });
      }
    }

    // Start a transaction to update invitation and create membership
    const result = await db.$transaction(async (prismaTransaction) => {
      // Update invitation status
      await prismaTransaction.workspaceInvite.update({
        where: { id: inviteId },
        data: { 
          status: 'ACCEPTED',
          userId: currentUser.uid, // Ensure userId is set
        },
      });

      // Create workspace membership
      const membership = await prismaTransaction.workspaceMembership.create({
        data: {
          userId: currentUser.uid,
          workspaceId: invitation.workspaceId!,
          roleId: roleId,
        },
      });

      return membership;
    });

    return NextResponse.json({
      message: 'Invitation accepted successfully',
      membership: result,
    });
  } catch (error) {
    console.error('Error accepting workspace invitation:', error);
    return NextResponse.json({ error: 'Failed to accept invitation' }, { status: 500 });
  }
}
