import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';

import { InvitationHandler } from '@/components/invitations/invitation-handler';
import { isValidInvitationToken } from '@/lib/utils/token';

interface InvitePageProps {
  params: Promise<{
    locale: string;
    token: string;
  }>;
}

export async function generateMetadata({ params }: InvitePageProps): Promise<Metadata> {
  const t = await getTranslations('workspace.invitations');

  return {
    title: t('pageTitle'),
    description: t('pageDescription'),
  };
}

export default async function InvitePage({ params }: InvitePageProps) {
  const { token } = await params;

  // Validate token format before proceeding
  if (!isValidInvitationToken(token)) {
    notFound();
  }

  return <InvitationHandler token={token} />;
}
